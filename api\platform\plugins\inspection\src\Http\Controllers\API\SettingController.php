<?php
namespace Botble\Inspection\Http\Controllers\API;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use Botble\Base\Http\Controllers\BaseController;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Throwable;
use Botble\Setting\Http\Controllers\Concerns\InteractsWithSettings;
use Bo<PERSON>ble\Setting\Facades\Setting;

// extends SettingController
class SettingController extends BaseController 
{
    use InteractsWithSettings;

    public function getGeneralSetting()
    {
        $logo = $this->getLogoUrl(Setting::get('logo'));
        
        $settings = [
            'logo' => $logo,
            'site_title' => Setting::get('site_title'),
            'site_description' => Setting::get('site_description'),
            'hotline' => Setting::get('hotline'),
            'private_settings' => Setting::get('private_settings') === 'true' ? true : false,
            'docusign_api_key' =>Setting::get('docusign_api_key'),
            'stripe_keys' =>Setting::get('stripe_keys'),
            'price_per_page' =>Setting::get('price_per_page'),
        ];

        return $this
            ->httpResponse()
            ->setData($settings)
            ->toApiResponse();
    }

    public function update(Request $request)
    {
        $data = [];

        if ($request->input('site_title')) {
            $data['site_title'] = $request->input('site_title');
        } 

        if ($request->input('site_description')) {
            $data['site_description'] = $request->input('site_description');
        }

        if ($request->input('hotline')) {
            $data['hotline'] = $request->input('hotline');
        }

        if ($request->input('private_settings')) {
            $data['private_settings'] = $request->input('private_settings');
        }

        if ($request->input('docusign_api_key')) {
            $data['docusign_api_key'] = $request->input('docusign_api_key');
        }

        if ($request->input('stripe_keys')) {
            $data['stripe_keys'] = $request->input('stripe_keys');
        }

        if (null !== $request->input('price_per_page')) {
            $data['price_per_page'] = $request->input('price_per_page');
        }

        if ($request->hasFile('logo') && $request->file('logo')->isValid()) {
            $file = $request->file('logo');
            $path = $file->store('images', 'public'); // stores in storage/app/public/images
            $data['logo'] = asset('storage/' . $path); // generate accessible URL
        }

        $this->saveSettings($data);

        return $this
        ->httpResponse()
        ->setData([])
        ->toApiResponse();
    }

    public function getLogoUrl(?string $path): ?string
    {
        if (!$path) {
            return '';
        }

        // If already a full URL, just return
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            return $path;
        }

        return asset('images/' . ltrim($path, '/'));
    }
}

