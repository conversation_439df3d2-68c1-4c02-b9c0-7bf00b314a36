import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import {
  Box,
  Button,
  Typography,
  Grid,
  LinearProgress,
} from '@mui/material';
import { InspectionFile, getInspectionFileById, fullTranslate, translationPreview, getPdfInfo, PdfFileStatus } from 'services/inspection-file.service';
import DocuSignButton from './DocuSignButton';
import DocuSignStatus from './DocuSignStatus';
import TranslationStepper, { StepKey, TranslationStepKey } from 'modules/inspectionFiles/TranslationStepper';
import CircularColor from 'modules/muiComponents/feedback/Progress/CircularColor';
import InspectionMetaData from './TranslationsStep/InspectionMetaData';
import TranslationActions from './TranslationsStep/TranslationActions';
import PdfPreview from './TranslationsStep/PdfPreview';
import TranslationCompletedStatus from './TranslationsStep/TranslationCompletedStatus';
import TranslationInprogressStatus from './TranslationsStep/TranslationInprogressStatus';

const InspectionDetail = () => {
  const router = useRouter();
  const { id, envelope_id } = router.query;
  const dispatch = useDispatch();
  const [fileDetail, setFileDetail] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [inspectionFile, setInspectionFile] = useState<InspectionFile | null>(null);
  const [pdfPreviewLoading, setPdfPreviewLoading] = useState(false);
  const [fullTranslationLoading, setFullTranslationLoading] = useState(false);
  const [pdfPreviewPath, setpdfPreviewPath] = useState<string>('');
  const [currentStep, setCurrentStep] = useState<StepKey>(TranslationStepKey.Upload);

  useEffect(() => {
    if (id) {
      fetchFileDetail(Number(id));
    }
  }, [id]);

  const fetchFileDetail = async (fileId) => {
    setLoading(true);
    const response = await getInspectionFileById(fileId);
    setLoading(false);

    console.log('response', response)

    if (response && response.data) {
      setInspectionFile(response.data);

      if (response.data?.preview_file_path) {
        setpdfPreviewPath(response.data?.preview_file_path);
      }

      handleSetTranslationStep(response.data);
    }
  };

  const handleSetTranslationStep = (inspectionFile: InspectionFile) => {
    if (inspectionFile?.status === PdfFileStatus.COMPLETED) {
      setCurrentStep(TranslationStepKey.Complete);
      return;
    }

    if (inspectionFile?.status === PdfFileStatus.IN_PROGRESS) {
      setCurrentStep(TranslationStepKey.Translation);
      return;
    }

    setCurrentStep(TranslationStepKey.Preview);
    return;
  }

  const handleFullTranslate = async () => {
    if (!inspectionFile) {
      return;
    }
    setCurrentStep(TranslationStepKey.Translation);
    setFullTranslationLoading(true);
    const res = await fullTranslate(inspectionFile.id);
    console.log('data', res)
    /**
     * TODO: check account balance -> show payment or navigate to Translateion
     */
    setFullTranslationLoading(false);

    //required payment
    if (res?.error && res?.data?.redirect_url) {
      alert('TODO: payment required and will redirect to stripe page');

      // router.push(res.data.redirect_url);
      router.push(res.data.callback_url);
      return;
    }

    setInspectionFile(res?.data);

    setCurrentStep(TranslationStepKey.Complete);
  }

  const handlePreviewGenerating = async () => {
    if (!inspectionFile) {
      return;
    }
    if (inspectionFile.preview_file_path) {
      setpdfPreviewPath(inspectionFile.preview_file_path);
    }

    setPdfPreviewLoading(true);
    const res = await translationPreview(inspectionFile.id);
    setPdfPreviewLoading(false);

    console.log('res', res)

    if (!res || res?.error) {
      alert('Error: ' + res?.error);
      return;
    }

    /**
     * setInspectionFile, to re- render PdfPreview component
     */
    setpdfPreviewPath(res.data.preview_file_path);
    setInspectionFile(res.data);
  }

  if (loading) {
    return <LoadingScreen />
  }

  if (error || !inspectionFile) {
    return (
      <Box>
        <Typography color="error">{error || 'Inspection file not found'}</Typography>
      </Box>
    )
  }

  const pdfFile = inspectionFile.pdf_file;

  return (
    <Grid container spacing={3} sx={{ mb: 6 }}>
      <Grid xs={12}>
        <TranslationStepper currentStep={currentStep} />
        <Grid xs={12} spacing={6} sx={{ mb: 6, mt: 8, display: 'flex' }}>
          <Grid xs={6}>
            {inspectionFile && <InspectionMetaData inspectionFile={inspectionFile} />}

            {envelope_id && (
              <DocuSignStatus envelopeId={String(envelope_id)} />
            )}

            {inspectionFile && inspectionFile.status === 'completed' && inspectionFile.translation && (
              <Box mt={3}>
                <DocuSignButton 
                  pdfFileId={inspectionFile.id} 
                  fileName={inspectionFile.pdf_file?.name || 'Document'} 
                  buttonProps={{
                    variant: 'contained',
                    color: 'primary',
                    sx: { marginRight: 2 }
                  }}
                  onSuccess={(envelopeId) => {
                    console.log(`Envelope created with ID: ${envelopeId}`);
                    // You could update UI or show a success message here
                  }}
                />
              </Box>
            )}
          </Grid>

          <Grid xs={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <TranslationActions
              inspectionFile={inspectionFile}
              loading={pdfPreviewLoading}
              fullTranslationLoading={fullTranslationLoading}
              pdfPreviewPath={pdfPreviewPath}
              onFullTranslate={handleFullTranslate}
              onPreviewGenerating={handlePreviewGenerating}
            />
          </Grid>
        </Grid>
      </Grid>

      {inspectionFile?.status === PdfFileStatus.COMPLETED && (
        <TranslationCompletedStatus inspectionFile={inspectionFile} />
      )}
      {(inspectionFile?.status === PdfFileStatus.IN_PROGRESS || fullTranslationLoading) && (
        <TranslationInprogressStatus inspectionFile={inspectionFile} />
      )}

      <PdfPreview
        originalPdfUrl={pdfFile.source_file}
        pdfPreviewUrl={pdfPreviewPath}
        loading={pdfPreviewLoading}
        inspectionFile={inspectionFile}
        onPreviewGenerating={handlePreviewGenerating}
      />
    </Grid>
  );
};


const LoadingScreen = () => (
  <Box
    sx={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
    }}
  >
    <CircularColor />
  </Box>
)

export default InspectionDetail;
