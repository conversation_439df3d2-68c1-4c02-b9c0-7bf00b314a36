import React, { useEffect, useState, useRef } from 'react';
import Grid from '@mui/material/Grid';
import {
  AppComponentCard,
  AppConfirmDialog,
  AppGridContainer,
  AppInfoView,
  AppSearchBar,
} from '@crema';
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridRowId,
} from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { Avatar, Badge, Box, Button, Chip, Typography, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material';
import { CloudUploadOutlined, Delete, Translate, MoreVert, Share, Print, Download } from '@mui/icons-material';
import InspectionCreatePopup from './InspectionCreatePopup';
import { InspectionFileObj } from 'types/models/apps/inspection-file.model';
import { useRouter } from "next/router";
import { AppState } from '../../redux/store';
import { BsEye } from 'react-icons/bs';
import MemberSelect from 'common/MemberSelect';
import StatusSelect from 'common/StatusSelect';
import dayjs from 'dayjs';
import { onGetInspectionFileList, onRemoveInsepctionFile } from 'redux/actions/inspection-file.action';
import { GET_INSPECTION_FILE_LIST } from 'types/actions/inspection-file.type';
import { PAGINATION } from 'shared/constants/AppConst';
import AppsHeader from '@crema/core/AppsContainer/AppsHeader';
import PdfFileStatus from 'common/PdfFileStatus';
import LanguageFlag from 'components/LanguageFlag';
import { BiLabel, BiPencil } from 'react-icons/bi';
import DocuSignButton, { DocuSignButtonRef } from './DocuSignButton';
import { printFile, copyToClipboard, downloadFile } from "services/file-actions.service";

interface InspectionGridProps {
  page: number;
  inspectionFileList: InspectionFileObj[];
  setPage: (page: number) => void;
  total: number;
  search: string;
  statusFilter?: string;
  memberFilter?: string;
}

const InspectionListing = () => {
  const [addPdfOpen, setAddPdfOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [filterText, setFilterText] = useState<string>('');
  const [page, setPage] = useState<number>(PAGINATION.page);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [memberFilter, setMemberFilter] = useState<string>('');
  const dispatch = useDispatch();
  const router = useRouter();

  const {
    inspectionFileList,
    total,
  }: { inspectionFileList: InspectionFileObj[], total: number } = useSelector<
    AppState,
    AppState['inspectionFileApp']
  >(({ inspectionFileApp }) => inspectionFileApp);

  // Parse URL query parameters
  useEffect(() => {
    const { status, member } = router.query;
    
    if (status && typeof status === 'string') {
      setStatusFilter(status);
    }
    
    if (member && typeof member === 'string') {
      setMemberFilter(member);
    }
  }, [router.query]);

  const handleFilterSearchInspection = () => {
    setPage(PAGINATION.page);
    setFilterText(searchText);
  }

  const onReloadTable = () => {
    dispatch(onGetInspectionFileList(GET_INSPECTION_FILE_LIST, {
      search: filterText,
      page: page + 1,
      per_page: PAGINATION.pageSize,
      status: statusFilter || undefined,
      member_id: memberFilter || undefined,
    }));
  }

  useEffect(() => {
    onReloadTable();
  }, [page, filterText, statusFilter, memberFilter, dispatch]);

  // Update URL when filters change
  const updateUrlWithFilters = (status?: string, memberId?: string) => {
    const query: { status?: string; member?: string } = {};
    
    if (status) query.status = status;
    if (memberId) query.member = memberId;
    
    router.push({
      pathname: router.pathname,
      query
    }, undefined, { shallow: true });
  };

  // Handle status selection
  const handleStatusChange = (status: string) => {
    const newStatus = status === 'all' ? '' : status;
    setStatusFilter(newStatus);
    updateUrlWithFilters(newStatus, memberFilter);
  };

  // Handle member selection
  const handleMemberChange = (memberId: string) => {
    setMemberFilter(memberId);
    updateUrlWithFilters(statusFilter, memberId);
  };

  return (
    <>
      <AppsHeader>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'right',
            width: 1,
            columnGap: 4
          }}
        >
          <AppSearchBar
            iconPosition='right'
            overlap={false}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              setSearchText(event.target.value)
            }
            placeholder={'Search here...'}
            onClickSearchIcon={() => handleFilterSearchInspection()}
            value={searchText}
            sx={{ flex: 1 }}
          />

          <MemberSelect
            onChange={handleMemberChange}
            placeholder="Select member..."
            value={memberFilter}
          />

          <StatusSelect
            onChange={handleStatusChange}
            placeholder="Filter by status"
            value={statusFilter}
          />

          <Box>
            <Button
              variant='contained'
              color='primary'
              onClick={() => setAddPdfOpen(true)}
              startIcon={<CloudUploadOutlined />}
            >
               Upload Inspection PDF
            </Button>
          </Box>
        </Box>
      </AppsHeader>

      <AppGridContainer>
        <Grid item xs={12}>
          <InspectionCreatePopup
            isOpen={addPdfOpen}
            onClose={() => setAddPdfOpen(false)}
            // onTableReload={() => onReloadTable()}
          />

          <AppComponentCard
            title=''
            component={DataGridComponent}
            noScrollbar
            description=''
            componentProps={{
              inspectionFileList,
              total,
              page,
              setPage,
              search: filterText,
              statusFilter,
              memberFilter
            }}
          />
        </Grid>
      </AppGridContainer>
      <AppInfoView />
    </>
  );
};

function DataGridComponent({ 
  page, 
  setPage, 
  inspectionFileList, 
  total, 
  search, 
  statusFilter, 
  memberFilter 
}: InspectionGridProps) {
  const dispatch = useDispatch();

  // const [detailOpen, setDetailOpen] = useState<boolean>(false);
  const [selectedInspectionId, setSelectedInspectionId] = useState<number | null>(null);
  const [confirmDeleteModal, setConfirmDeleteModal] = useState<boolean>(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const docuSignButtonRef = useRef<DocuSignButtonRef>(null);

  const router = useRouter();

  const inspectionFileFormatter = inspectionFileList.map((item) => {
    return {
      ...item,
      account_name: item.member?.name || '',
    };
  });


  const handleEditClick = (id: GridRowId) => () => {
    router.push(`/inspection-files/${id}`)
  };

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', flex: 40 },
    {
      field: 'name',
      headerName: 'File Name',
      flex: 250,
      renderCell: (params) => {
        return (
          <div tabIndex={params.hasFocus ? 0 : -1} style={{ cursor: 'pointer' }}>
            {params.row.pdf_file.name}
          </div>
        );
      },
    },
    {
      field: 'language',
      headerName: 'Language',
      flex: 250,
      align: 'center',
      renderCell: (params) => {
        return (
          <LanguageFlag
            key={params.row.id}
            language={params.row.language}
            label={''}
          />
        );
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => {
        return <PdfFileStatus status={params.row.status} size="small" />
      },
    },
    {
      field: 'account_name',
      headerName: 'Cost',
      width: 150,
      renderCell: (params) => {
        return (
          <Typography variant='body2' color='text.secondary'>
            ${params.row.price}
          </Typography>
        );
      },
    },
    {
      field: 'account_name',
      headerName: 'Inspector',
      width: 150,
      renderCell: (params) => {
        return (
          <Chip
            title={params.row.user.first_name || params.row.user.email}
            avatar={<Avatar>{params.row.user.email.charAt(0)}</Avatar>}
            icon={<Translate />}
            sx={{ textTransform: 'capitalize' }}
            size='small'
            label={params.row.user.first_name || params.row.user.email}
            color={'default'}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              router.push(`/members/${params.row.user.id}`)
            }}
          />
        );
      },
    },

    {
      field: 'created_at',
      headerName: 'Date',
      width: 100,
      renderCell: (params) => {
        return (
          <Typography variant='body2' color='text.secondary'>
            {dayjs(params.row.created_at).format('MMMM D, YYYY')}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 300,
      cellClassName: 'actions',
      align: 'right',
      renderCell: (params) => {
        return (
          <>
            {params.row.status === 'completed' ? <GridActionsCellItem
                icon={<Download />}
                label='Delete'
                onClick={() => {
                  window.open(params.row.pdf_file.source_file, "_blank")
                }}
                color='inherit'
              /> : <div style={{ minWidth: 10 }}></div>}
            <GridActionsCellItem
              icon={<BsEye />}
              label='Translate'
              className='textPrimary'
              onClick={handleEditClick(params.row.id)}
              color='inherit'
            />
            
            <GridActionsCellItem
              icon={<MoreVert />}
              label='Delete'
              onClick={(event) => {
                event.stopPropagation();
                setAnchorEl(event.currentTarget);
                setSelectedInspectionId(params.row.id);
              }}
              color='inherit'
            />
            <Menu
              id="inspection-menu"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl) && selectedInspectionId === params.row.id}
              onClose={() => setAnchorEl(null)}
            >
              <MenuItem onClick={() => {
                router.push(`/inspection-files/${selectedInspectionId}/translate`);
                setAnchorEl(null);
              }}>
                <ListItemIcon>
                  <Translate fontSize="small" />
                </ListItemIcon>
                <ListItemText sx={{ fontSize: 14 }}>Translate again</ListItemText>
              </MenuItem>
              <MenuItem onClick={() => {
                router.push(`/inspection-files/${selectedInspectionId}/translate`);
                setAnchorEl(null);
              }}>
                <ListItemIcon>
                  <Translate fontSize="small" />
                </ListItemIcon>
                <ListItemText sx={{ fontSize: 14 }}>Translate to another language</ListItemText>
              </MenuItem>
              <MenuItem onClick={() => {
                setAnchorEl(null);
              }}>
                <ListItemIcon>
                  <BiLabel fontSize="small" />
                </ListItemIcon>
                <ListItemText>Invoice</ListItemText>
              </MenuItem>
              <MenuItem onClick={() => {
                // Open the DocuSign dialog when this menu item is clicked
                if (docuSignButtonRef.current) {
                  docuSignButtonRef.current.openDialog();
                }
              }}>
                <ListItemIcon>
                  <BiPencil fontSize="small" />
                </ListItemIcon>
                <ListItemText>Sign documents (Docusign)</ListItemText>
              </MenuItem>
              <MenuItem onClick={async () => {
                // Get the file URL
                const fileUrl = params.row.translation?.translated_file_path || params.row.pdf_file?.source_file;
                if (!fileUrl) {
                  // Show error message
                  alert('No file available to share');
                  setAnchorEl(null);
                  return;
                }
                
                // Copy link to clipboard
                const success = await copyToClipboard(fileUrl);
                if (success) {
                  alert('Link copied to clipboard');
                } else {
                  alert('Failed to copy link');
                }
                setAnchorEl(null);
              }}>
                <ListItemIcon>
                  <Share fontSize="small" />
                </ListItemIcon>
                <ListItemText>Share</ListItemText>
              </MenuItem>
              <MenuItem onClick={() => {
                // Handle share action
                setAnchorEl(null);
              }}>
                <ListItemIcon>
                  <Print fontSize="small" />
                </ListItemIcon>
                <ListItemText>Print</ListItemText>
              </MenuItem>
              <MenuItem onClick={() => {
                setConfirmDeleteModal(true);
                setAnchorEl(null);
              }}>
                <ListItemIcon>
                  <Delete fontSize="small" />
                </ListItemIcon>
                <ListItemText>Delete</ListItemText>
              </MenuItem>
            </Menu>
          </>
        );
      },
    },
  ];


  return (
    <div style={{ height: 400, width: '100%' }}>
      {/* Hidden DocuSign button that can be triggered programmatically */}
      {selectedInspectionId && (
        <DocuSignButton
          ref={docuSignButtonRef}
          pdfFileId={selectedInspectionId}
          fileName={inspectionFileList.find(item => item.id === selectedInspectionId)?.pdf_file?.name || 'Document'}
          showButton={false}
          onSuccess={(envelopeId) => {
            // Handle success if needed
            console.log(`Envelope created with ID: ${envelopeId}`);
          }}
          onError={(message) => {
            // Handle error if needed
            console.error(`DocuSign error: ${message}`);
          }}
        />
      )}
      
      <DataGrid
        sx={{
          '& .MuiDataGrid-columnHeaders': {
            fontSize: '0.9rem',
          },
        }}
        rows={inspectionFileFormatter}
        columns={columns}
        paginationMode='server'
        rowCount={total}
        pageSize={PAGINATION.pageSize}
        page={page}
        onPageChange={(newPage) => setPage(newPage)}
        rowsPerPageOptions={[10]}
        checkboxSelection
        disableSelectionOnClick
        onRowClick={(params) => {
          router.push(`/inspection-files/${params.row.id}`)
        }}
      />

      <AppConfirmDialog
        open={confirmDeleteModal}
        onDeny={() => setConfirmDeleteModal(false)}
        onConfirm={async () => {
          await dispatch(onRemoveInsepctionFile(selectedInspectionId as number));

          setConfirmDeleteModal(false);
          dispatch(onGetInspectionFileList(GET_INSPECTION_FILE_LIST, {
            search,
            page: page + 1,
            per_page: PAGINATION.pageSize,
            status: statusFilter,
            member_id: memberFilter,
          }))
        }}
        title='Are you sure you want to delete this inspection file?'
        dialogTitle='Delete Inspection File'
      />
    </div>
  );
}

export default InspectionListing;
