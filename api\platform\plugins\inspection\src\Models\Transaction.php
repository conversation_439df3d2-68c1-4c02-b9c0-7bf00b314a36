<?php

namespace Bo<PERSON>ble\Inspection\Models;

use Botble\ACL\Models\User;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Botble\Inspection\Enums\TransactionStatus;

class Transaction extends BaseModel
{
    protected $fillable = [
        'amount',
        'user_id', 
        'status',
        'company_id',
        'user_pdf_file_id'
    ];

    protected $casts = [
        'status' => TransactionStatus::class, 
    ];


    public function inpsectionFile(): BelongsTo
    {
        return $this->belongsTo(UserPdfFile::class, 'user_pdf_file_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function isCompleted(): bool
    {
        return $this->status === TransactionStatus::COMPLETED;
    }
}