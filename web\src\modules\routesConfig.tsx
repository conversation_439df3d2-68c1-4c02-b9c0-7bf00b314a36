import {ReactNode} from 'react';
import {RoutePermittedRole} from '../shared/constants/AppConst';
import {FaStripeS} from 'react-icons/fa';
import {FiActivity, FiUsers} from 'react-icons/fi';
import {BsBriefcase, BsFilePdf} from 'react-icons/bs';
import {HiInformationCircle, HiOutlineChartSquareBar} from 'react-icons/hi';
import SettingsIcon from '@mui/icons-material/Settings';
import { Email, QuestionAnswer, Support} from '@mui/icons-material';
import BusinessIcon from '@mui/icons-material/Business';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import FolderIcon from '@mui/icons-material/Folder';
import BorderColorIcon from '@mui/icons-material/BorderColor';

export interface RouterConfigData {
  id: string;
  title: string;
  icon?: string | ReactNode;
  type: 'item' | 'group' | 'collapse' | 'divider';
  children?: RouterConfigData[];
  permittedRole?: RoutePermittedRole;
  color?: string;
  url?: string;
  exact?: boolean;
  count?: number;
  as?: string;
}

const routesConfig: RouterConfigData[] = [
  {
    id: 'page-dashboard',
    title: 'Dashboard',
    type: 'item',
    icon: <HiOutlineChartSquareBar />,
    url: '/dashboard',
  },
  {
    id: 'page-member',
    title: 'Members',
    type: 'item',
    icon: <FiUsers />,
    url: '/members',
  },
  {
    id: 'page-inspection-file',
    title: 'Inspection file',
    type: 'collapse',
    icon: <BsFilePdf />,
    url: '/inspection-files',
    children: [
      {
        id: 'page-inspection-file',
        title: 'All',
        type: 'item',
        icon: <FolderIcon  sx={{  color: '#3B82F6' }} />,
        url: '/inspection-files',
      },
      {
        id: 'page-inspection-files-1',
        title: 'Inprogress',
        type: 'item',
        icon: <FolderIcon  sx={{  color: '#F97316' }} />,
        url: '/inspection-files?status=inprogress',
      },
      {
        id: 'page-inspection-files-2',
        title: 'Completed',
        type: 'item',
        icon: <FolderIcon  sx={{  color: '#10B981' }} />,
        url: '/inspection-files?status=completed',
      },
    ]
  },
  {
    id: 'page-report',
    title: 'Report',
    // messageId: 'sidebar.report',
    type: 'item',
    icon: <BsBriefcase />,
    url: '/report',
  },
  {
    id: 'page-setting',
    title: 'Settings',
    // messageId: 'sidebar.user',
    type: 'collapse',
    icon: <SettingsIcon />,
    url: '/settings',
    children: [
      {
        id: 'page-general',
        title: 'General',
        type: 'item',
        icon: <SettingsIcon />,
        url: '/settings',
      },
      {
        id: 'page-email-templates',
        title: 'Email templates',
        type: 'item',
        icon: <Email />,
        url: '/settings/email-templates',
      },
      {
        id: 'page-docusign-keys',
        title: 'DocuSign Keys',
        type: 'item',
        icon: <BorderColorIcon />,
        url: '/settings/docusign-keys',
      },
      {
        id: 'page-stripe-keys',
        title: 'Stripe Keys',
        type: 'item',
        icon: <FaStripeS />,
        url: '/settings/stripe-keys',
      },
    ],
  },
  
  {
    id: 'page-activities',
    title: 'Activities',
    type: 'item',
    icon: <FiActivity />,
    url: '/activities',
  },
  {
    id: 'page-help',
    title: 'Help',
    type: 'item',
    icon: <Support />,
    url: '/help',
  },
  {
    id: 'super-admin',
    title: 'Super admin',
    type: 'group',
    children: [
      {
        id: 'page-company',
        title: 'Clients/Agents',
        type: 'item',
        icon: <BusinessIcon />,
        url: '/companies',
      },
      {
        id: 'page-plan',
        title: 'Subscriptions',
        type: 'item',
        icon: 'attach_money',
        url: '/subscriptions',
      },
      {
        id: 'page-coupon',
        title: 'Coupons',
        type: 'item',
        icon: <LocalOfferIcon />,
        url: '/coupons',
      },
    ],
  },
  {
    id: 'page-help',
    title: 'About',
    type: 'item',
    icon: <HiInformationCircle />,
    url: '/about',
  },
  {
    id: 'page-help',
    title: 'Feedback',
    type: 'item',
    icon: <QuestionAnswer />,
    url: '/feedbacks',
  },
  
];
export default routesConfig;
