import React from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import {useThemeContext} from '../../../../../@crema/utility/AppContextProvider/ThemeContextProvider';

interface StatGraphsProps {
  data: {date: string, count: number}[]
}

const StatGraphs: React.FC<StatGraphsProps> = ({data}) => {
  const {theme} = useThemeContext();

  return (
    <ResponsiveContainer width='100%' height={350}>
      <AreaChart data={data} margin={{top: 25, right: 0, left: 0, bottom: 0}}>
        <defs>
          <linearGradient id='colorPv' x1='0' y1='0' x2='0' y2='1'>
            <stop
              offset='5%'
              stopColor={theme.palette.secondary.main}
              stopOpacity={0.7}
            />
            <stop
              offset='95%'
              stopColor={theme.palette.secondary.main}
              stopOpacity={0}
            />
          </linearGradient>
        </defs>
        <XAxis
          dataKey='date'
          tickLine={false}
        />
        <Tooltip labelStyle={{color: 'black'}} />
        <YAxis 
          tickLine={false}
        />
        <CartesianGrid
          strokeDasharray='2 10'
          horizontal={false}
          vertical={false}
        />
        <Area
          type='monotone'
          dataKey='count'
          stroke={theme.palette.secondary.main}
          strokeWidth={3}
          fillOpacity={1}
          fill='url(#colorPv)'
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default StatGraphs;
