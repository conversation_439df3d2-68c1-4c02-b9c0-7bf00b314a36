import jwtAxios from '@crema/services/auth/jwt-auth';
import { PaymentStatusType } from 'common/PaymentStatus';
import { Dispatch } from 'redux';
import { fetchError, fetchStart, fetchSuccess } from 'redux/actions/Common';
import { AppActions } from 'types';

export const getPdfFileList = (dispatch: Dispatch<AppActions>) => {
  dispatch(fetchStart());

  return jwtAxios
    .get('inspection-files')
    .then((data) => {
      dispatch(fetchSuccess());

      if (data.status === 200) {
        return data.data;
      } else {
        dispatch(fetchError('somethingWentWrong'));
      }
    })
    .catch((error) => {
      dispatch(fetchError(error.message));
    });
};

export const translationPreview = (inspectionId: number) => {

  // let bodyFormData = new FormData();
  // bodyFormData.append('source_file', pdfFile);
  // bodyFormData.append('languages', languages.join(','));

  return jwtAxios
    .get(`inspection-files/${inspectionId}/translation-preview`)
    .then((data) => {
      // dispatch(fetchSuccess());

      if (data.status === 200) {
        return data.data;
      } else {
        // dispatch(fetchError('somethingWentWrong'));
      }

      return null;
    })
    .catch((error) => {
      console.log('error: ', error);
      return null;
      // dispatch(fetchError(error.message));
    });
};


export const updatePaymentStatus = (inspectionId: number) => {

  return jwtAxios
    .post(`inspection-files/payment/${inspectionId}/status`)
    .then((data) => {
      if (data.status === 200) {
        return data.data;
      } else {
        // dispatch(fetchError('somethingWentWrong'));
      }

      return null;
    })
    .catch((error) => {
      console.log('error: ', error);
      return null;
      // dispatch(fetchError(error.message));
    });
};

export const fullTranslate = (inspectionId: number) => {

  // let bodyFormData = new FormData();
  // bodyFormData.append('source_file', pdfFile);
  // bodyFormData.append('languages', languages.join(','));

  return jwtAxios
    .get(`inspection-files/${inspectionId}/translate`)
    .then((data) => {
      if (data.status === 200) {
        return data.data;
      } else {
        // dispatch(fetchError('somethingWentWrong'));
      }

      return null;
    })
    .catch((error) => {
      console.log('error: ', error);
      return {
        error: error?.response?.data || error.message || 'Unknown error',
        status: error?.response?.status || 500,
      };
      // dispatch(fetchError(error.message));
    });
};

export const createInspectionFile = (
  dispatch: Dispatch<AppActions>,
  pdfFile, language
) => {
  dispatch(fetchStart());

  let bodyFormData = new FormData();
  bodyFormData.append('source_file', pdfFile);
  bodyFormData.append('language', language);

  return jwtAxios
    .post('inspection-files', bodyFormData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    .then((data) => {
      dispatch(fetchSuccess());

      if (data.status === 200) {
        return data.data;
      } else {
        dispatch(fetchError('somethingWentWrong'));
      }
    })
    .catch((error) => {
      dispatch(fetchError(error.message));
    });
};



export const getInspectionFileDetail = (dispatch: Dispatch<AppActions>, id: number) => {
  dispatch(fetchStart());

  return jwtAxios
    .get(`pdf-files/${id}`)
    .then((data) => {
      console.log('fetch data', data);
      dispatch(fetchSuccess());

      if (data.status === 200) {
        return data.data;
      } else {
        dispatch(fetchError('somethingWentWrong'));
      }
    })
    .catch((error) => {
      dispatch(fetchError(error.message));
    });
};

export const PdfFileStatus = {
  PENDING: 'pending',
  IN_PROGRESS: 'inprogress',
  COMPLETED: 'completed',
  REJECTED: 'rejected',
}


export interface InspectionFile {
  id: number;
  status: typeof PdfFileStatus[keyof typeof PdfFileStatus];
  created_at: string;
  company_id: number;
  pdf_file_id: number;
  user_id: number;
  payment_status: typeof PaymentStatusType[keyof typeof PaymentStatusType];
  preview_file_path: string;
  language: string;  
  translation: Translation;
  transaction_id: string;
  pdf_file: {
    id: number;
    name: string;
    source_file: string;
    file_type: string;
    file_size: number;
    status: string;
    created_at: string;
    updated_at: string;
    md5: string;
    member_id: number;
    company_id: number;
    // translations: Translation[];
    page_count?: number;
  };
  user: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export interface Translation {
  id: number;
  language: string;
  translated_file_path: string;
  created_at: string;
  two_page_path: string;
  status: string;
}

interface InspectionFileResponse {
  error: boolean;
  data: InspectionFile;
  message: string | null;
}

export const getInspectionFileById = async (id: string): Promise<InspectionFileResponse> => {
  const response = await jwtAxios.get(`inspection-files/${id}`);
  return response.data;
};

export const getPdfFileByMd5 = async (hash: string): Promise<InspectionFileResponse> => {
  const response = await jwtAxios.get(`pdf-files/${hash}`);
  return response.data;
}; 

export const getPdfInfo = async (id: string): Promise<any> => {
  const response = await jwtAxios.get(`inspection-files/${id}/pdf-info`);
  return response.data;
};