import {Dispatch} from 'redux';
import {AppActions} from '../../types';
import {fetchError, fetchStart, fetchSuccess, showMessage} from './Common';
import {
  GET_INSPECTION_FILE,
    GET_INSPECTION_FILE_LIST,
    REMOVE_INSPECTION_FILE_SUCCESS,
} from '../../types/actions/inspection-file.type';
import jwtAxios from '@crema/services/auth/jwt-auth';
import { QueryProps, GetProps, SUCCESS_CODES } from 'common/lavarel/restapi';


interface InspectionFileQueryProps extends QueryProps {
  status?: string;
  member_id?: string;
}

export const onGetInspectionFileList = (
  type: string,
  queryProps?: InspectionFileQueryProps
) => {
  return (dispatch: Dispatch<AppActions>) => {
    dispatch(fetchStart());
    jwtAxios
      .get('inspection-files', {
        params: {
          type: type,
          ...queryProps
        },
      })
      .then((data) => {
        console.log('data', data.data.data)
        if (data.status === 200) {
          dispatch(fetchSuccess());
          dispatch({type: GET_INSPECTION_FILE_LIST, payload: data.data.data});
        } else {
          dispatch(
            fetchError('somethingWentWrong'),
          );
        }
      })
      .catch((error) => {
        dispatch(fetchError(error.message));
      });
  };
};

export const onRemoveInsepctionFile = (id: number) => {
  return async (dispatch): Promise<any> => {
    try {
      dispatch(fetchStart());
      const response = await jwtAxios.delete(`inspection-files/${id}`);
      if (SUCCESS_CODES.includes(response.status)) {
        dispatch({type: REMOVE_INSPECTION_FILE_SUCCESS, payload: response.data});
      } else {
        const errorMessage = 'somethingWentWrong';
        dispatch(fetchError(errorMessage));
        throw new Error(errorMessage);
      }
    } catch (error) {
      dispatch(fetchSuccess());
      throw error;
    }
  };
};

export const onGetInspectionFile = (
  type: string,
  queryProps?: GetProps
) => {
  
  return (dispatch: Dispatch<AppActions>) => {
    dispatch(fetchStart());
    jwtAxios
      .get('inspection-files/', {
        params: {
          type: type,
          ...queryProps
        },
      })
      .then((data) => {
        console.log('data', data.data.data)
        if (data.status === 200) {
          dispatch(fetchSuccess());
          dispatch({type: GET_INSPECTION_FILE, payload: data.data});
        } else {
          dispatch(
            fetchError('somethingWentWrong'),
          );
        }
      })
      .catch((error) => {
        dispatch(fetchError(error.message));
      });
  };
};


