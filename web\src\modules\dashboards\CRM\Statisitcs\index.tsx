import React from 'react';
import GraphTabs from './GraphTabs';
import AppCard from '../../../../@crema/core/AppCard';

interface StatisticsProps {
  translationChartData: {date: string; count: number}[];
}

export const Statistics: React.FC<StatisticsProps> = ({
  translationChartData
}) => {
  return (
    <AppCard sxStyle={{height: 1}}>
      <GraphTabs
        translationChartData={translationChartData}
      />
    </AppCard>
  );
};

export default Statistics;
