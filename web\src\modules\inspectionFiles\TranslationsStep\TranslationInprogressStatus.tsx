import { CheckCircle } from "@mui/icons-material";
import { Box, LinearProgress, Typography } from "@mui/material";
import { InspectionFile } from "services/inspection-file.service";


interface CompleteStepProps {
  inspectionFile: InspectionFile;
}

const TranslationInprogressStatus: React.FC<CompleteStepProps> = ({ inspectionFile }) => {
  let text = 'The PDF translation is in inprogress...'
  if (inspectionFile.payment_status !== 'paid') {
    text = 'Please wait a moment — translation will begin once payment is confirmed.'
  }

 return (
   <Box sx={{ width: '100%', p: 10, textAlign: 'center', gap: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
    <Typography sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} color={'text.primary'} variant='h4' mt={2}>
      {text}
    </Typography>
    <Box sx={{ width: '70%' }}>
      <LinearProgress color="secondary" variant="indeterminate" sx={{ mb: 2 }} />
    </Box>
  </Box>
 )
}

export default TranslationInprogressStatus;