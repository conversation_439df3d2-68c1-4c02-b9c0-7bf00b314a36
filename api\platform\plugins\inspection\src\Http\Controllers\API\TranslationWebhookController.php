<?php

namespace Botble\Inspection\Http\Controllers\API;

use Botble\Base\Models\AdminNotification;
use Botble\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TranslationWebhookController extends BaseController
{
    public function handle(Request $request)
    {
        $validated = $request->validate([
            'md5' => 'required|string',
            'translated_pdf_path' => 'required|string',
        ]);

        AdminNotification::query()->create([
            'title' => 'Pdf translation completed',
            'action_label' => 'Pdf translation completed',
            'action_url' => '#',
            'description' => 'Pdf translation completed : ' . $validated['md5'],
        ]);

        Log::info('Translation completed webhook received', [
            'md5' => $validated['md5'],
            'translated_pdf_path' => $validated['translated_pdf_path'],
        ]);

        return response()->json(['status' => 'ok'], 200);
    }
}
