import jwtAxios from '@crema/services/auth/jwt-auth';

export interface TransactionCallBackProps {
    status: string;
    transaction_id: number;
}

export const transactionCallback = async (data: TransactionCallBackProps): Promise<any> => {
  try {
    const response = await jwtAxios.post(`transactions/callback`, data);
    return response.data;
  } catch (error) {
    throw error;
  }
}; 

export interface Transaction {
  id: number;
  amount: number;
  status: string;
  user?: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  user_pdf_file_id: number;
  inspection_file?: {
    id: number;
    name: string;
    status: string;
  };
  created_at: string;
}

export const getTransactionById = async (id: string): Promise<any> => {
  try {
    const response = await jwtAxios.get(`transactions/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};