import React from 'react';
import {alpha, Box} from '@mui/material';
import {Fonts} from '../../../../shared/constants/AppEnums';
import AppCard from '../../../../@crema/core/AppCard';
import {QuickStatsData} from 'types/models/dashboards/CRM';

interface TotalRevenueProps {
  revenueData: QuickStatsData;
}

const TotalRevenue: React.FC<TotalRevenueProps> = ({revenueData}) => {
  return (
    <>
      <Box
        component='h2'
        sx={{
          textTransform: 'uppercase',
          color: 'text.primary',
          fontSize: 16,
          mb: {xs: 4, sm: 4, xl: 6},
          fontWeight: Fonts.BOLD,
        }}
      >
        Total Revenue
      </Box>
      <AppCard>
        <Box
          sx={{
            display: 'flex',
            flexDirection: {xs: 'column', sm: 'row'},
          }}
        >
          <Box
            sx={{
              mr: {xs: 2, xl: 10},
              p: 5,
              display: 'flex',
              flexDirection: 'column',
              color: 'text.secondary',
              backgroundColor: (theme) =>
                alpha(theme.palette.primary.main, 0.1),
            }}
          >
            <Box
              sx={{
                mb: 4,
              }}
            >
              <Box
                sx={{
                  mb: 0.75,
                  color: 'text.primary',
                  fontWeight: Fonts.MEDIUM,
                  fontSize: 18,
                }}
                component='h3'
              >
                {revenueData.transaction_amount}
              </Box>
              <Box
                component='p'
                sx={{
                  mb: 0,
                  fontSize: 14,
                }}
              >
                Revenue
              </Box>
            </Box>
            <Box
              sx={{
                mt: 'auto',
                mx: {xs: -2, xl: -5},
                mb: 1,
                display: 'flex',
              }}
            >
              <Box
                sx={{
                  px: {xs: 2, xl: 5},
                }}
              >
                <Box
                  sx={{
                    mb: 0.75,
                    color: "#0A8FDC",
                    fontWeight: Fonts.MEDIUM,
                    fontSize: 18,
                    textDecoration: "none"
                  }}
                  component='a'
                  href="/members"
                >
                  {revenueData.member_total}
                </Box>
                <Box
                  component='p'
                  sx={{
                    mb: 0,
                    fontSize: 14,
                  }}
                >
                  Members
                </Box>
              </Box>

              <Box
                sx={{
                  px: {xs: 2, xl: 5},
                }}
              >
                <Box
                  sx={{
                    mb: 0.75,
                    color: "#49BD65",
                    fontWeight: Fonts.MEDIUM,
                    fontSize: 18,
                    textDecoration: "none"
                  }}
                  component='a'
                  href='/companies'
                >
                  {revenueData.company_total}
                </Box>
                <Box
                  component='p'
                  sx={{
                    mb: 0,
                    fontSize: 14,
                  }}
                >
                  Companies
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </AppCard>
    </>
  );
};

export default TotalRevenue;
