# xdebug support
https://github.com/archielite/docker-php/blob/main/docs/xdebug-support.md


# Environment

### Clone .env.example file to .env

# SWAGGER
`http://127.0.0.1:9000/docs`

`Generating Swagger: php artisan scribe:generate`

# portal
## Run with sqlite
- Update .env with DATABASE=sqlite
- Comment database settings.
- In folder database, create database.sqlite file.
- Run ```php artisan migrate``` to generate database schema.
## Register cms plugins
- With fresh database, we cannot access plugin controllers such as MemberController.
- Run ``` php artisan cms:plugin:activate ``` to activate a plugin then type your plugin name.

## Symlinks API folder to host, share uploads file with Translation container
``ln -s /docker/storage/ files``

### Queue
# development
`php artisan queue:work`

# production
`php artisan supervisor:manage start`

# Docusign
1. Call success 20 requests on develop mode
2. Register consent via URL: https://account-d.docusign.com/oauth/auth?response_type=code&scope=signature%20impersonation&client_id=b88081c7-6400-4032-80b1-3963eeac9cca&redirect_uri=http://localhost:9000/api/v1/docusign/webhook 