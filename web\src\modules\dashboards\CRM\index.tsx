import React, {useEffect} from 'react';
import {Grid} from '@mui/material';
import TotalRevenue from './TotalRevenue';
import QuickStats from './QuickStats';
import Statistics from './Statisitcs';
import {useDispatch, useSelector} from 'react-redux';
import {onGetStatisticReportData} from '../../../redux/actions';
import AppGridContainer from '@crema/core/AppGridContainer';
import AppInfoView from '@crema/core/AppInfoView';
import AppAnimate from '../../../@crema/core/AppAnimate';
import {AppState} from '../../../redux/store';
import Members from './Members';
import { QuickStatsData } from 'types/models/dashboards/CRM';


const CRM = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(onGetStatisticReportData())
  }, [dispatch]);

  const {statisticData}: {statisticData: QuickStatsData} = useSelector(
    (state: AppState) => state.report,
  );

  return (
    <>
      {statisticData ? (
        <AppAnimate animation='transition.slideUpIn' delay={200}>
          <AppGridContainer>
            <Grid item xs={12} md={5}>
              <TotalRevenue revenueData={statisticData} />
            </Grid>
            <Grid item xs={12} md={7}>
              <QuickStats quickStatsData={statisticData} />
            </Grid>

            <Grid item xs={12} md={6}>
              <Statistics
                translationChartData={statisticData.translation_chart_data}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Members />
            </Grid>
            
          </AppGridContainer>
        </AppAnimate>
      ) : null}

      <AppInfoView />
    </>
  );
};

export default CRM;
