import { <PERSON>, Button, CircularProgress, Divider, Grid, LinearProgress, Typography } from "@mui/material";
import { InspectionFile } from "services/inspection-file.service";
import NavigateNextIcon from '@mui/icons-material/NavigateNext';

type Props = {
  originalPdfUrl: string;
  pdfPreviewUrl?: string;
  loading: boolean;
  inspectionFile: InspectionFile;
  onPreviewGenerating: () => void;
};

const PdfPreview = ({ originalPdfUrl, pdfPreviewUrl, loading, inspectionFile, onPreviewGenerating }: Props) => {
  console.log('pdfPreviewUrl', pdfPreviewUrl)
  console.log('inspectionFile', inspectionFile)
  
  return (
    <>
    <Divider orientation='vertical' flexItem sx={{ my: 2 }} />
    <Grid md={12} sx={{ position: 'relative', display: 'flex'}} gap={15}>      
      <Grid xs={12} md={6} sx={{   }}>
        <Box>
          <Typography variant='h5' sx={{ mb: 3 }}>
            Original PDF
          </Typography>
          <iframe
            src={originalPdfUrl}
            style={{ width: '100%', height: 400, border: '1px solid #ccc' }}
          />
        </Box>
      </Grid>

      <Grid item xs={12} md={6} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column', border: pdfPreviewUrl ? 'none' : '2px dotted #ccc', borderRadius: pdfPreviewUrl ? 'none' : '5px' }}>
        {!pdfPreviewUrl && <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column'}}>
          <Button
            sx={{ width: 'fit-content' }}
            size='large'
            variant='outlined'
            color='primary'
            onClick={onPreviewGenerating}
            endIcon={loading ? <CircularProgress size={16} color='inherit' /> : <NavigateNextIcon />}
          >
            View trial translation
          </Button>
        </Box>}

        {loading ? (
          <Box sx={{ mt: 5, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              Preview Generating...
            </Typography>
            <Box sx={{ width: '70%' }}>
              <LinearProgress sx={{ mb: 2 }} />
            </Box>
          </Box>
        ) : pdfPreviewUrl ? (
          <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', mb: 4 }}>
            <Typography variant='h5' sx={{ mb: 3 }}>
              Translated PDF Preview
            </Typography>
            <iframe
              src={pdfPreviewUrl}
              style={{ width: '100%', height: 400, border: '1px solid #ccc' }}
            />
          </Box>
        ) : null}
      </Grid>
    </Grid>
    </>
  )
}

export default PdfPreview;