version: '3.8'


services:
  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: always
    mem_limit: 512MB
    environment:
      MYSQL_DATABASE: golden_dashboard
      MYSQL_ROOT_PASSWORD: Golden@2025
    ports:
      - "3306:3306"
    volumes:
      - ./db/dbdata:/var/lib/mysql
      - ./db/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
       - shared-network
    logging:
      driver: "json-file"
      options:
          max-file: "5"
          max-size: "10m"

networks:
  shared-network:
    external: true
    name: shared-network