import { useRouter } from "next/router";
import { useEffect } from "react";
import { transactionCallback } from "services/transaction.service";

const TransactionCallback = () => {
    const router = useRouter();
    const { id } = router.query;
    console.log('id', id)

    useEffect(() => {
        const handleCallback = async() => {
            if (!id) {
                return;
            }
            const res = await  transactionCallback({
                status: 'completed',
                transaction_id: typeof id === 'string' ? parseInt(id) : parseInt(id[0]),
            });
            console.log('res', res)

            router.push(`/inspection-files/${res?.data?.id}`);
        };

        handleCallback();
    }, [id, router]);

    return <div>Transaction in progress...</div>;
};

export default TransactionCallback;
