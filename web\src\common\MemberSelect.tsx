import React, { useEffect, useState } from 'react';
import { FormControl, InputLabel, MenuItem, Select, SelectChangeEvent } from '@mui/material';
import { useDispatch } from 'react-redux';
import { fetchError, fetchStart, fetchSuccess } from 'redux/actions/Common';
import jwtAxios from '@crema/services/auth/jwt-auth';

interface MemberSelectProps {
  onChange: (memberId: string) => void;
  placeholder?: string;
  value?: string;
}

interface Member {
  id: number;
  first_name: string;
  email: string;
}

const MemberSelect: React.FC<MemberSelectProps> = ({ onChange, placeholder = 'Select member', value = '' }) => {
  const [members, setMembers] = useState<Member[]>([]);
  const [selectedMember, setSelectedMember] = useState<string>(value);
  const dispatch = useDispatch();

  useEffect(() => {
    // Set the selected member when the value prop changes
    if (value !== undefined) {
      setSelectedMember(value);
    }
  }, [value]);

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const response = await jwtAxios.get('users');
        console.log('response', response.data.data);
        if (response.data && response.data.data) {
          setMembers(response.data.data.data);
        }
      } catch (error) {
        console.error('Error fetching members:', error);
        dispatch(fetchError('Failed to fetch members'));
      }
    };

    fetchMembers();
  }, [dispatch]);

  const handleChange = (event: SelectChangeEvent<string>) => {
    const memberId = event.target.value;
    setSelectedMember(memberId);
    onChange(memberId);
  };

  return (
    <FormControl sx={{ minWidth: 200 }}>
      <InputLabel id="member-select-label">{placeholder}</InputLabel>
      <Select
        labelId="member-select-label"
        id="member-select"
        value={selectedMember}
        label={placeholder}
        onChange={handleChange}
        displayEmpty
      >
        <MenuItem value="">
          <em>All members</em>
        </MenuItem>
        {members && members.map((member) => (
          <MenuItem key={member.id} value={member.id.toString()}>
            {member.first_name || member.email}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default MemberSelect; 
