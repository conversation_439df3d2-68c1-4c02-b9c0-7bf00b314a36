import { AppComponent<PERSON>ard, AppConfirmD<PERSON>og, AppGridContainer, AppSearchBar } from '@crema';
import { Checkbox, Grid, Box, Button, Chip, IconButton, Avatar, Link } from '@mui/material';
import MemberCreatePopup from './MemberCreatePopup';

import { DataGrid, GridActionsCellItem, GridColDef } from '@mui/x-data-grid';
import { ListStateType } from 'common/states';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppState } from 'redux/store';
import { MemberModel } from 'types/models/apps/member.model';
import { clearSelectedUser, onGetMemberList, onRemoveMember, onUpdateMember } from 'redux/actions/member.action';
import MemberDetailDialog from './MemberDetailPopup';
import { Check, Delete, Edit, PlusOne } from '@mui/icons-material';
import AppsHeader from '@crema/core/AppsContainer/AppsHeader';
import AppsContent from '@crema/core/AppsContainer/AppsContent';
import { BiPlus } from 'react-icons/bi';
import {PAGINATION} from 'shared/constants/AppConst';
import {useRouter} from 'next/router';
import PdfFileStatus from 'common/PdfFileStatus';
import MemberRole from 'common/MemberRole';


export type MemberListStateType = ListStateType<MemberModel>;
interface MemberGridProps {
  page: number;
  data: any;
  setPage: (page: number) => void;
  search: string;
}

const MemberList = () => {
  const [memberDialogOpen, setMemberDialogOpen] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [filterText, setFilterText] = useState<string>('');
  const [page, setPage] = useState<number>(PAGINATION.page);

  const dispatch = useDispatch();

  const {data}: any = useSelector((state: AppState) => state.userList);

  const handleFilterSearchMember = () => {
	  setPage(PAGINATION.page);
    setFilterText(searchText);    
  }

  const onReloadTable = () => {
    dispatch(
      onGetMemberList({
        search: filterText,
        page: page + 1,
        per_page: PAGINATION.pageSize,
      })
    );
  }
	
  useEffect(() => {
    onReloadTable();
  }, [page, filterText, dispatch])

  return (
    <>
      <AppsHeader>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'right',
            width: 1,
          }}
        >
          <AppSearchBar
            iconPosition='right'
            overlap={false}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              setSearchText(event.target.value)
            }
            placeholder={'Search here'}
            onClickSearchIcon={() => handleFilterSearchMember()}
            value={searchText}
          />
          <Box>
            <Button
              size='medium'
              startIcon={<BiPlus />}
              variant='contained'
              color='primary'
              onClick={() => setMemberDialogOpen(true)}
            >
              Add Member
            </Button>
            <MemberCreatePopup
              isOpen={memberDialogOpen}
              onClose={() => {setMemberDialogOpen(false)}}
              onGetMemberList={() => {onReloadTable()}}
            />
          </Box>
        </Box>
      </AppsHeader>

      <AppsContent>
        <Grid item xs={12}>
          <AppComponentCard
            title=''
            component={MemberGrid}
            noScrollbar
            description=''
            componentProps={{
              page,
              setPage,
              data,
              search: filterText
            }}
          />
        </Grid>
      </AppsContent>
    </>
  );
};

const MemberGrid = ({page, setPage, data, search}: MemberGridProps) => {
  const [selectedMemberId, setSelectedMemberId] = useState<null | number>(null);
  const [confirmDeleteModal, setConfirmDeleteModal] = useState<boolean>(false);

  const router = useRouter();

  const dispatch = useDispatch();

  const rows = () => {
    // return users!.data!.data || [];
    return data!.data || [];
  };

  const onReloadTable = () => {
    dispatch(
      onGetMemberList({
        search,
        page: page + 1,
        per_page: PAGINATION.pageSize,
      })
    );
  }

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 90,
      headerClassName: 'super-app-theme--header',
    },
    {
      field: 'first_name',
      headerName: 'Name',
      flex: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params) => {        
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Avatar sx={{ width: 22, height: 22 }}>{params.row.first_name.charAt(0).toUpperCase()}</Avatar>
            <Box
              component='span'
              sx={{
                ml: 3.5,
                fontWeight: 500,
              }}
            >
              {params.row.first_name} {params.row.last_name}
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'email',
      headerName: 'Email',
      flex: 150,
    },
    {
      field: 'phone',
      headerName: 'Phone',
      flex: 150,
    },
    {
      field: 'phone',
      headerName: 'Subscription',
      flex: 150,
    },
    {
      field: 'role',
      headerName: 'Role',
      flex: 150,
      renderCell: (params) => {
        return (
          <MemberRole role={params.row.role} size="small" variant='outlined'/>
        );
      }
    },
    {
      field: 'is_external',
      headerName: 'External?',
      flex: 150,
      renderCell: (params) => {        
        return (
          <Box
              component='span'
            >
              {params.row?.is_external ? <Check /> : ""}
            </Box>
        );
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 150,
      renderCell: (params) => {
        return (
          <PdfFileStatus status={params.row.status} size="small" />
        );
      }
    },
    {
      field: 'total_pdf',
      headerName: 'Inspection Files',
      flex: 150,
      renderCell: (params) => {
        return (
          <Link
            href={`/inspection-files?member=${params.row.id}`}
            component='a'
            underline="none"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
            }}
          >
            {params.row.total_pdf}
          </Link>
        );
      }
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 100,
      cellClassName: 'actions',
      renderCell: (params) => {
        return (
          <>
            <GridActionsCellItem
              icon={<Edit />}
              label="Edit"
              showInMenu={false}
              className='textPrimary'
              color='inherit'
              onClick={() => {
                router.push(`/members/${params.row.id}`)
              } }
            />

            <GridActionsCellItem
              icon={<Delete />}
              label="Delete"
              showInMenu={false}
              className='textPrimary'
              color='inherit'
              onClick={() => {
                setConfirmDeleteModal(true)
                setSelectedMemberId(params.row.id)
              }}
            />
          </>
        );
      },
    },
  ];

  const handleEditRow = (id: number) => {
    // setSelectedMemberId(id);
    router.push(`/members/${id}`)
  };

  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        initialState={{
          sorting: {
            sortModel: [{ field: 'id', sort: 'desc' }],
          },
        }}
        sx={{
          '& .MuiDataGrid-columnHeaders': {
            fontSize: '0.9rem',
          },
        }}
        rows={rows()}
        columns={columns}
        paginationMode='server'
        rowCount={data.total}
        pageSize={PAGINATION.pageSize}
        page={page}
        onPageChange={(newPage) => setPage(newPage)}
        rowsPerPageOptions={[5]}
        checkboxSelection
        disableSelectionOnClick
        onRowClick={(params: any, event) => {
          handleEditRow(params.id);
        }}
      />

      {/* {selectedMemberId && (<MemberDetailDialog
        id={selectedMemberId}
        open={!!selectedMemberId}
        onClose={() => {
          setSelectedMemberId(null);
          dispatch(clearSelectedUser())
        }}
        onGetMemberList={() => onReloadTable()}
      />)} */}

      <AppConfirmDialog 
        open={confirmDeleteModal}
        onDeny={() => setConfirmDeleteModal(false)}
        onConfirm={async () => {
          await dispatch(onRemoveMember(selectedMemberId as number))
          setConfirmDeleteModal(false);
          onReloadTable()
        }}
        title='Are you sure you want to delete this member?'
        dialogTitle='Delete Member'
      />
    </div>
  );
};

export default MemberList;
