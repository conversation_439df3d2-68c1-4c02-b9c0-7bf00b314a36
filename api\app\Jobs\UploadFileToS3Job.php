<?php

namespace App\Jobs;

use Bo<PERSON>ble\Inspection\Models\Translation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UploadFileToS3Job implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $fileUrl;
    protected $fileName;
    protected $translationId;

    /**
     * Create a new job instance.
     *
     * @param string $fileUrl
     * @param string $fileName
     * @param int $translationId
     * @return void
     */
    public function __construct(string $fileUrl, string $fileName, int $translationId)
    {
        $this->fileUrl = $fileUrl;
        $this->fileName = $fileName;
        $this->translationId = $translationId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        Log::info("Starting S3 upload job for file: " . $this->fileName);
        
        try {
            // Get file content from URL
            $fileContent = file_get_contents($this->fileUrl);
            if (!$fileContent) {
                Log::error("Failed to get content from file URL: " . $this->fileUrl);
                return;
            }

           // Generate a unique filename for the translated file
            $pathInfo = pathinfo($this->fileUrl);
            \Log::info(print_r( $pathInfo, true));
            $originalName = $pathInfo['filename'];
            $extension = $pathInfo['extension'] ?? 'pdf';
            
            $fileName = $originalName . '.' . $extension;
            \Log::info("Translated file name: " . $fileName);
            $s3Path = 'uploads/translated/' . $fileName;

            // Upload to S3 (Contabo)
            Storage::disk('contabo')->put($s3Path, $fileContent, [
                'visibility' => 'public'
            ]);
            
            // Get the public URL
            $publicUrl = Storage::disk('contabo')->url($s3Path);
            
            Log::info("File uploaded to S3: " . $publicUrl);
            
            // Update the translation record with the new S3 URL
            $translation = Translation::find($this->translationId);
            if ($translation) {
                $translation->translated_file_path = $publicUrl;
                $translation->save();
                
                Log::info("Translation record updated with S3 URL for ID: " . $this->translationId);
            } else {
                Log::error("Translation record not found for ID: " . $this->translationId);
            }
            
        } catch (\Exception $e) {
            Log::error('Error in UploadFileToS3Job: ' . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('UploadFileToS3Job failed: ' . $exception->getMessage());
    }
}