<?php

namespace Bo<PERSON>ble\Inspection\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Inspection\Enums\PaymentStatus;
use Bo<PERSON>ble\Inspection\Enums\TransactionStatus;
use Bo<PERSON>ble\Inspection\Models\Transaction;
use Botble\Inspection\Models\UserPdfFile;
use Botble\Inspection\Repositories\Interfaces\TransactionInterface;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Webhook;

class StripeWebhookController extends BaseController
{
    public function __construct(protected TransactionInterface $transactionRepository)
    {
    }

    public function handle(Request $request)
    {
        $input = $request->all();
        $transactionId = $input['transaction_id'];

        $transaction = Transaction::query()
        ->where('id', $transactionId)->firstOrFail();

        $inspectionFile = $transaction->inpsectionFile;

        $transaction->status = TransactionStatus::COMPLETED;
        $transaction->save();

        $inspectionFile->payment_status = PaymentStatus::PAID;
        $inspectionFile->transaction_id = $transaction->id;
        $inspectionFile->save();


         return $this->httpResponse()
                ->setMessage('Payment successfully')
                ->setData($inspectionFile->toArray())
                ->toApiResponse();


        // $curentUser = auth()->user();
        // $token = auth()->user()?->currentAccessToken();
        // $companyId = $token->company_id ?? config('constants.COMPANY_DEFAULT');

        // $endpointSecret = config('services.stripe.webhook_secret'); // .env

        // $payload = $request->getContent();
        // $sigHeader = $request->header('Stripe-Signature');

        // try {
        //     $event = Webhook::constructEvent(
        //         $payload, $sigHeader, $endpointSecret
        //     );
        // } catch (\UnexpectedValueException $e) {
        //     Log::error('Webhook payload error', ['error' => $e->getMessage()]);
        //     return response('Invalid payload', 400);
        // } catch (\Stripe\Exception\SignatureVerificationException $e) {
        //     Log::error('Webhook signature error', ['error' => $e->getMessage()]);
        //     return response('Invalid signature', 400);
        // }

     
        // switch ($event->type) {
        //     case 'checkout.session.completed':
        //         $session = $event->data->object;
             
        //         $orderId = $session->client_reference_id;
        //         $customerEmail = $session->customer_details->email;

        //         Log::info("Checkout completed", [
        //             'order_id' => $orderId,
        //             'email' => $customerEmail,
        //         ]);

        //         break;

        //     case 'payment_intent.succeeded':
        //         $paymentIntent = $event->data->object;

        //         Log::info("Payment succeeded", [
        //             'id' => $paymentIntent->id,
        //             'amount' => $paymentIntent->amount_received
        //         ]);

        //         //$orderId = $event->data->object->client_reference_id;

        //         Transaction::query()->create([
        //             'amount' => $paymentIntent->amount_received,
        //             'user_id' => $curentUser->id, 
        //             'status' => TransactionStatus::COMPLETED,
        //             'company_id' => $companyId,
        //             'user_pdf_file_id' => 0   //todo
        //         ]);

        //         /**
        //          * Update payment_status on UserPdfFile
        //          */

                
                

        //         break;

        //     default:
        //         Log::info("Unhandled event type: " . $event->type);
        // }

        // return $this->httpResponse()
        //         ->setData([
        //             'message' => 'Webhook received'
        //         ])
        //         ->toApiResponse();
    }
}