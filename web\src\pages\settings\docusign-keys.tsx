import { AppComponentHeader, AppContentView } from '@crema';
import AppPage from '@crema/hoc/AppPage';
import asyncComponent from '@crema/utility/asyncComponent';
import { Box } from '@mui/material';
import React from 'react';

const DocusignKeys = asyncComponent(() => import('../../modules/settings/DocusignKeys'));

const DocusignKeyPage = () => {
  return (
    <>
      <AppComponentHeader 
        title='Docusign Key'
        description='View and manage docusign key information '
      />
      <DocusignKeys />
    </>
  );
};


export default AppPage(() => <DocusignKeyPage />); 