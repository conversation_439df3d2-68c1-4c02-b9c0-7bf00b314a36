import { AppComponentHeader, AppContentView } from '@crema';
import AppPage from '@crema/hoc/AppPage';
import asyncComponent from '@crema/utility/asyncComponent';
import { Box } from '@mui/material';
import React from 'react';

const InspectionDataTable = asyncComponent(
  () => import('../../modules/inspectionFiles/InspectionListing'),
);

const InspectionPage = () => {
  return (
    <>
      <AppComponentHeader
        title="Inspection files"
        description="View all Inspection files"
      />
      <InspectionDataTable />
    </>
  );
};

export default AppPage(React.memo(() => <InspectionPage />));
