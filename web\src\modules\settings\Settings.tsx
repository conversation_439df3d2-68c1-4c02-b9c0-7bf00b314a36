import React, { useEffect, useState } from 'react';
import { Alert, Box, Button, Grid, Switch, TextField } from '@mui/material';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import AppCard from '@crema/core/AppCard';
import { useDropzone } from 'react-dropzone';
import { getSettings, updateSettings } from '../../services/setting.service';
// import { message } from 'antd';

const validationSchema = Yup.object().shape({
  site_title: Yup.string().required('Site Title is required'),
  site_description: Yup.string().required('Site Description is required'),
  hotline: Yup.string().required('Hotline is required'),
});

const Settings = () => {
  const [initialValues, setInitialValues] = useState({
    logo: '',
    site_description: '',
    site_title: '',
    private_settings: false,
    hotline: '',
    price_per_page: 0
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [logoPreview, setLogoPreview] = useState('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await getSettings();
      if (response.data) {
        setInitialValues(response.data);
        if (response.data.logo) {
          setLogoPreview(response.data.logo);
        }
      }
    } catch (error) {
     //Failed to load settings
    }
  };

  const onDrop = (acceptedFiles: File[], setFieldValue: (field: string, value: any) => void) => {
    const file = acceptedFiles[0];
    if (file) {
      setFieldValue('logo', file);
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <AppCard>
      <Box
        component="h2"
        sx={{
          color: 'text.primary',
          fontSize: 16,
          mb: 6,
          fontWeight: 'bold',
        }}
      >
        Settings
      </Box>
      {error && (
        <Alert severity='error' sx={{mb: 2}}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity='success' sx={{mb: 2}}>
          Settings updated successfully!
        </Alert>
      )}
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize={true}
        onSubmit={async (values, {setSubmitting}) => {
          try {
            setError(null);
            setSuccess(false);

            const formData = new FormData();
            formData.append('site_title', values.site_title);
            formData.append('site_description', values.site_description);
            formData.append('hotline', values.hotline);
            formData.append('private_settings', values.private_settings.toString());
            formData.append('price_per_page', values.price_per_page.toString());

            // Append logo file if it exists
            if (values.logo) {
              formData.append('logo', values.logo);
            }
             
            const isError = await updateSettings(formData);

            if (isError) {
              setError('Failed to update settings');
              return;
            }

            setSuccess(true);
          } catch (error) {            
            setError('Failed to update settings');
          } finally {
            setSubmitting(false);
          }
        }}
      >
        {({ values, errors, touched, handleChange, handleBlur, handleSubmit, setFieldValue, isSubmitting }) => {
          
        const { getRootProps, getInputProps } = useDropzone({
          accept: '.png,.jpg,.jpeg',
          multiple: false,
          onDrop: (acceptedFiles) => onDrop(acceptedFiles, setFieldValue),
        });

        return (
          <Form onSubmit={handleSubmit}>
            <Grid container spacing={5}>
              <Grid item xs={12}>
                <Box
                  {...getRootProps()}
                  sx={{
                    cursor: 'pointer',
                    border: '1px dashed grey',
                    p: 2,
                    textAlign: 'center',
                    mb: 4,
                    maxWidth: '400px',
                  }}
                >
                  <input {...getInputProps()} />
                  {logoPreview ? (
                    <img src={logoPreview} alt="logo" style={{ maxWidth: '200px' }} />
                  ) : (
                    <p>Drag 'n' drop logo here, or click to select file</p>
                  )}
                </Box>

                <TextField
                  fullWidth
                  id="site_title"
                  name="site_title"
                  label="Site Title"
                  value={values.site_title}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.site_title && Boolean(errors.site_title)}
                  helperText={touched.site_title && errors.site_title}
                  sx={{ mb: 4, maxWidth: '600px' }}
                />

                <TextField
                  fullWidth
                  id="site_description"
                  name="site_description"
                  label="Site Description"
                  multiline
                  rows={4}
                  value={values.site_description}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.site_description && Boolean(errors.site_description)}
                  helperText={touched.site_description && errors.site_description}
                  sx={{ mb: 4, maxWidth: '600px' }}
                />

                <TextField
                  fullWidth
                  id="hotline"
                  name="hotline"
                  label="Hotline"
                  value={values.hotline}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.hotline && Boolean(errors.hotline)}
                  helperText={touched.hotline && errors.hotline}
                  sx={{ mb: 4, maxWidth: '600px' }}
                />

                <TextField
                  type="number"
                  fullWidth
                  id="price_per_page"
                  name="price_per_page"
                  label="Price per page"
                  value={values.price_per_page}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.price_per_page && Boolean(errors.price_per_page)}
                  helperText={touched.price_per_page && errors.price_per_page}
                  sx={{ mb: 4, maxWidth: '600px' }}
                />

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                  <Switch
                    checked={values.private_settings}
                    onChange={(e) => setFieldValue('private_settings', e.target.checked)}
                    name="private_settings"
                  />
                  <Box component="span" sx={{ ml: 2 }}>
                    Private Settings
                  </Box>
                </Box>

                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  Save Settings
                </Button>
              </Grid>
            </Grid>
          </Form>
        )}}
      </Formik>
    </AppCard>
  );
};

export default Settings; 