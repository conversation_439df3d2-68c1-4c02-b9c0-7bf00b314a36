import React from 'react';
import { Chip, ChipProps } from '@mui/material';
import { PdfFileStatus as PdfFileStatusType } from 'services/inspection-file.service';

export const PaymentStatusType = {
  UNPAID: 'unpaid',
  PAID: 'paid',
  FAILED: 'failed',
}


interface PaymentStatusProps {
  status: typeof PaymentStatusType[keyof typeof PaymentStatusType];
  size?: 'small' | 'medium';
  variant?: 'filled' | 'outlined';
}

const PaymentStatus: React.FC<PaymentStatusProps> = ({ 
  status, 
  size = 'small',
  variant = 'outlined'
}) => {
  const getStatusColor = (): ChipProps['color'] => {
    if (!status) {
      return 'default';
    }
    switch (status) {
      case 'pending':
        return 'default';
      case 'inprogress':
        return 'warning';
      case 'completed':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Chip 
      size={size} 
      label={status ? (status.charAt(0).toUpperCase() + status.slice(1)) : 'N/A'} 
      color={getStatusColor()} 
      variant={variant}
      sx={{ textTransform: 'capitalize', fontSize: 12 }}
    />
  );
};

export default PaymentStatus;