import { CheckCircle, Download } from "@mui/icons-material";
import { Box, Button, Typography } from "@mui/material";
import { InspectionFile } from "services/inspection-file.service";


interface CompleteStepProps {
  inspectionFile: InspectionFile;
}

const TranslationCompletedStatus: React.FC<CompleteStepProps> = ({ inspectionFile }) => (
  <Box sx={{ width: '100%', p: 4, textAlign: 'center', gap: 4 }}>
    <Typography sx={{  display: 'flex', alignItems: 'center', gap: 2 , justifyContent: 'center' }} variant='h4' mt={2}>
      <CheckCircle color='success' fontSize='large' /> The PDF translation is completed!
    </Typography>
    <Box sx={{ display: 'flex', mt: 6, mb: 6, justifyContent: 'center' }}>
      <Button target="_blank" href={inspectionFile?.translation?.translated_file_path || '#'} variant="contained" color='secondary' startIcon={<Download />} size='large'>
        DOWNLOAD INSPECTION PDF
      </Button>
    </Box>
  </Box>
);

export default TranslationCompletedStatus;