<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],
    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
    ],
    'docusign' => [
        'client_id' => env('DOCUSIGN_CLIENT_ID'),
        'client_secret' => env('DOCUSIGN_CLIENT_SECRET'),
        'base_url' => env('DOCUSIGN_BASE_URL', 'https://demo.docusign.net/restapi'),
        'account_id' => env('DOCUSIGN_ACCOUNT_ID'),
        'impersonated_user_id' => env('DOCUSIGN_IMPERSONATED_USER_ID'),
        'user_id' => env('DOCUSIGN_USER_ID'),
    ],
];
