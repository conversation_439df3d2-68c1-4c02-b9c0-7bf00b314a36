<?php

namespace Botble\Inspection\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Inspection\Models\Transaction;
use Botble\Inspection\Repositories\Interfaces\TransactionInterface;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TransactionController extends BaseController
{
    public function __construct(protected TransactionInterface $transactionRepository)
    {
        
    }

    public function index(Request $request)
    {
        $currentPage = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');

        $query = Transaction::query();

        $search = trim($search || '');

        if (!empty($search)) {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }

        $transactions = $query->paginate($perPage, ['*'], 'page', $currentPage);

        return $this
            ->httpResponse()
            ->setData($transactions)
            ->toApiResponse();
    }

    public function show($id)
    {
        $transaction = Transaction::query()->findOrFail($id);
        // $transaction->inpsectionFile = $transaction->inpsectionFile;
        $transaction->user = $transaction->user;

        return $this
            ->httpResponse()
            ->setData($transaction)
            ->toApiResponse();
    }

}