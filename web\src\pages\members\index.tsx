import {AppComponentHeader, AppContentView} from '@crema';
import AppPage from '@crema/hoc/AppPage';
import asyncComponent from '@crema/utility/asyncComponent';
import React from 'react';

const Member = asyncComponent(
  () => import('../../modules/member/MemberListing'),
);

const MemberPage = () => {
  return (
    <>
      <AppComponentHeader 
        title='Members'
        description='View all Members'
      />
      <Member />
    </>
  );
};

export default AppPage(() => <MemberPage />);
