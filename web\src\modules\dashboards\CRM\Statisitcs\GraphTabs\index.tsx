import React, {useState} from 'react';
import Tabs from '@mui/material/Tabs';
import Box from '@mui/material/Box';
import {Fonts} from '../../../../../shared/constants/AppEnums';
import StatGraphs from './StatGraphs';

interface StatisticsProps {
  translationChartData: {date: string, count: number}[]
}

const GraphTabs: React.FC<StatisticsProps> = ({
  translationChartData
}) => {
  const [value, setValue] = useState(0);

  const handleChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box
      sx={{
        width: 1,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: {xs: 'column', md: 'row'},
          alignItems: {md: 'center'},
        }}
      >
        <Box
          component='h3'
          sx={{
            fontWeight: Fonts.BOLD,
            fontSize: 16,
          }}
        >
        Inspection file Statistics
        </Box>

        <Box
          sx={{
            mt: {md: -2},
            flex: 1,
            display: 'flex',
            flexDirection: {xs: 'column', md: 'row'},
            alignItems: {md: 'center'},
          }}
        >
          <Tabs
            value={value}
            onChange={handleChange}
            indicatorColor='primary'
            textColor='primary'
            aria-label='simple tabs example'
            sx={{
              flex: '1',
              position: 'relative',
              '& .MuiTabs-flexContainer': {
                justifyContent: {md: 'center'},
              },
              '& .crMuiTab': {
                minWidth: '10px',
                textTransform: 'capitalize',
                padding: 0,
                mx: {xs: 2, xl: 3.5},
                fontSize: 14,
              },
            }}
          >
          </Tabs>
        </Box>
      </Box>
      <Box
        sx={{
          mt: 4,
        }}
      >
        <StatGraphs data={translationChartData} />
      </Box>
    </Box>
  );
};

export default GraphTabs;
