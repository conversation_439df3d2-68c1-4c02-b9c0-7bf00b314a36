import {MemberActions} from './actions/member.type';
import {CommonActionTypes} from './actions/Common.type';
import {SettingsActionTypes} from './actions/settings.type';
import {DashboardActionTypes} from './actions/dashboard.type';
import {EcommerceActionTypes} from './actions/ecommerce.type';
import {AuthActions} from './actions/Auth.actions';
import {MailActions} from './actions/mail.type';
import {WalltActions} from './actions/wall.type';
// import {ScrumboardActions} from './actions/scrumboard.type';
import {UserListActions} from './actions/user-list.type';
import {InspectionFileActions} from './actions/inspection-file.type';

export type AppActions =
  | CommonActionTypes
  | SettingsActionTypes
  | DashboardActionTypes
  | EcommerceActionTypes
  | AuthActions
  | MailActions
  | WalltActions
  | InspectionFileActions
  | UserListActions
  | MemberActions;
