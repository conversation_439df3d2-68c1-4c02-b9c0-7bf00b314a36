import React, { useEffect, useState } from 'react';
import { Box, Typography, Button, CircularProgress, Paper } from '@mui/material';
import { useDispatch } from 'react-redux';
import { getEnvelopeStatus, downloadSignedDocument } from 'services/docusign.service';
// import { useSnackbar } from 'notistack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import ErrorIcon from '@mui/icons-material/Error';
import DownloadIcon from '@mui/icons-material/Download';

interface DocuSignStatusProps {
  envelopeId: string;
}

const DocuSignStatus: React.FC<DocuSignStatusProps> = ({ envelopeId }) => {
  const dispatch = useDispatch();
  // const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState<string | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [downloadLoading, setDownloadLoading] = useState(false);

  useEffect(() => {
    if (!envelopeId) return;
    
    const fetchStatus = async () => {
      setLoading(true);
      const response = await getEnvelopeStatus(dispatch, envelopeId);
      setLoading(false);
      
      if (response) {
        setStatus(response.status);
        
        // If completed, try to get the download URL
        if (response.status === 'completed') {
          handleDownload();
        }
      }
    };
    
    fetchStatus();
    
    // Poll for status updates every 10 seconds if not completed
    // const interval = setInterval(() => {
    //   if (status !== 'completed') {
    //     fetchStatus();
    //   }
    // }, 10000);
    
    // return () => clearInterval(interval);
  }, [envelopeId, dispatch]);

  const handleDownload = async () => {
    if (downloadUrl) {
      window.open(downloadUrl, '_blank');
      return;
    }
    
    setDownloadLoading(true);
    const response = await downloadSignedDocument(dispatch, envelopeId);
    setDownloadLoading(false);
    
    if (response) {
      setDownloadUrl(response.download_url);
      window.open(response.download_url, '_blank');
    } else {
      // enqueueSnackbar('Failed to download signed document', { variant: 'error' });
      alert('Failed to download signed document')
    }
  };

  const renderStatusIcon = () => {
    if (loading) return <CircularProgress size={24} />;
    
    switch (status) {
      case 'completed':
        return <CheckCircleIcon color="success" fontSize="large" />;
      case 'sent':
      case 'delivered':
        return <PendingIcon color="warning" fontSize="large" />;
      case 'declined':
      case 'voided':
        return <ErrorIcon color="error" fontSize="large" />;
      default:
        return <PendingIcon color="info" fontSize="large" />;
    }
  };

  const renderStatusText = () => {
    if (loading) return 'Checking signature status...';
    
    switch (status) {
      case 'completed':
        return 'Document has been signed successfully!';
      case 'sent':
        return 'Document has been sent for signature.';
      case 'delivered':
        return 'Document has been delivered to the signer.';
      case 'declined':
        return 'Signer has declined to sign the document.';
      case 'voided':
        return 'The signature request has been voided.';
      default:
        return `Document status: ${status}`;
    }
  };

  if (!envelopeId) return null;

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Box display="flex" alignItems="center" mb={2}>
        {renderStatusIcon()}
        <Typography variant="h6" ml={2}>
          DocuSign Status
        </Typography>
      </Box>
      
      <Typography variant="body1" mb={3}>
        {renderStatusText()}
      </Typography>
      
      {status === 'completed' && (
        <Button
          variant="contained"
          color="primary"
          startIcon={downloadLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
          onClick={handleDownload}
          disabled={downloadLoading}
        >
          Download Signed Document
        </Button>
      )}
    </Paper>
  );
};

export default DocuSignStatus;