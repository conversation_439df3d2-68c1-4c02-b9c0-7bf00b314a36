import { AppComponentHeader, AppContentView } from '@crema';
import AppPage from '@crema/hoc/AppPage';
import asyncComponent from '@crema/utility/asyncComponent';
import { Box } from '@mui/material';
import React from 'react';

const StripeKeys = asyncComponent(() => import('../../modules/settings/StripeKeys'));

const StripeKeyPage = () => {
  return (
    <>
      <AppComponentHeader 
        title='Stripe Key'
        description='View and manage stripe key information '
      />
      <StripeKeys />
    </>
  );
};


export default AppPage(() => <StripeKeyPage />);
