{"name": "hister", "version": "0.1.0", "private": true, "dependencies": {"@babel/runtime": "^7.15.4", "@date-io/date-fns": "^2.11.0", "@date-io/moment": "^2.11.0", "@emotion/cache": "^11.7.1", "@emotion/react": "^11.7.1", "@emotion/server": "^11.4.0", "@emotion/styled": "^11.6.0", "@mui/icons-material": "^5.2.5", "@mui/lab": "^5.0.0-alpha.61", "@mui/material": "^5.2.5", "@mui/styles": "^5.2.3", "@mui/x-data-grid": "^5.2.1", "@mui/x-data-grid-generator": "^5.2.1", "@mui/x-data-grid-pro": "^5.2.1", "@next/bundle-analyzer": "^15.4.0-canary.51", "@next/env": "^15.2.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "apexcharts": "^3.29.0", "autosuggest-highlight": "^3.2.0", "axios": "^0.21.4", "axios-mock-adapter": "^1.20.0", "clsx": "^1.1.1", "connected-react-router": "^6.9.1", "css-mediaquery": "^0.1.2", "date-fns": "^2.23.0", "dayjs": "^1.11.13", "formik": "^2.2.9", "formik-material-ui-pickers": "^1.0.0-alpha.1", "json2mq": "^0.2.0", "material-ui-popup-state": "^2.0.0", "moment": "^2.29.1", "next": "^12.0.7", "next-transpile-modules": "^9.0.0", "notistack": "^3.0.2", "pluralize": "^8.0.0", "prism-react-renderer": "^1.2.1", "prop-types": "^15.7.2", "raw-loader": "^4.0.2", "react": "^17.0.2", "react-apexcharts": "^1.3.9", "react-bottom-scroll-listener": "^5.0.0", "react-code-input": "^3.10.1", "react-content-loader": "^6.0.3", "react-dom": "^17.0.2", "react-draggable": "^4.4.4", "react-dropzone": "^11.4.0", "react-icons": "^4.2.0", "react-notifications-component": "^3.1.0", "react-number-format": "^4.8.0", "react-popper": "^2.2.5", "react-redux": "^7.2.5", "react-router-dom": "^5.3.0", "react-router-redux": "^4.0.8", "react-style-proptype": "^3.2.2", "react-swipeable-views": "^0.14.0", "react-virtualized": "^9.22.3", "react-window": "^1.8.6", "recharts": "^2.1.2", "redux": "^4.1.1", "redux-thunk": "^2.3.0", "simplebar-react": "^2.3.5", "stylis": "^4.0.10", "typescript": "^4.4.3", "yup": "^0.32.9"}, "scripts": {"dev": "next", "build": "next build", "analyze": "cross-env ANALYZE=true yarn build", "start": "next start", "post-update": "echo \"codesandbox preview only, need an update\" && yarn upgrade --latest", "lint": "eslint \"src/**/*.+(js|jsx)\"", "lint:fix": "eslint --fix \"src/**/*.+(js|jsx)\"", "format": "prettier --write \"src/**/*.+(js|jsx)\"", "precommit": "lint-staged"}, "browserslist": {"production": [">0.5%", "last 2 versions", "not dead", "not op_mini all", "not IE 11"], "development": ["last 1 chrome version"]}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@types/jest": "^27.0.3", "@types/node": "^16.11.11", "@types/react": "^17.0.37", "@types/react-dom": "^17.0.11", "@types/react-notifications-component": "^3.1.1", "@types/react-window": "^1.8.5", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "babel-plugin-inline-react-svg": "^2.0.1", "eslint": "^8.7.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^7.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.28.0", "husky": "^4.3.7", "lint-staged": "^10.5.3", "prettier": "^2.2.1"}}