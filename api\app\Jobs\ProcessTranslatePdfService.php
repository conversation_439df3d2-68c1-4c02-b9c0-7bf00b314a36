<?php

namespace App\Jobs;

use Botble\ACL\Models\User;
use Botble\Inspection\Enums\InspectionFileStatus;
use Botble\Inspection\Models\PdfFile;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Botble\Base\Facades\EmailHandler;
use Botble\Base\Models\AdminNotification;
use Botble\Inspection\Models\Translation;
use Botble\Inspection\Models\UserPdfFile;
use Illuminate\Support\Facades\Storage;

class ProcessTranslatePdfService implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected UserPdfFile $inspectionFile;
    protected $pdfFile;
    protected $args;

    public function __construct(UserPdfFile $inspectionFile, array $args = [])
    {
        $this->inspectionFile = $inspectionFile;
        $this->pdfFile = $inspectionFile->pdfFile;
        $this->args = $args;
    }

    public function handle(): void
    {
        Log::info("Start send file to API:" . $this->pdfFile->id);
        \set_time_limit(0);

        $pdfTranslationServiceUrl = env('PDF_TRANSLATION_SERVICE_URL') . '/translate';
        
        // $filePath = public_path('storage/' .  $this->pdfFile->source_file);
        // $filePublicUrl = asset('storage/' .  $this->pdfFile->source_file);

        // if (!file_exists($filePath)) {
        //     Log::error('File không tồn tại:' .  $filePath);
        //     return;
        // }

        try {
            $payload = array_merge([
                "fileName" => $this->pdfFile->name,
                "pdf_url" => $this->pdfFile,
                // "pdf_file_id" => $this->pdfFile->id,
                // "path" => "./tests/samples/sample-02-mold-1-3.pdf"
            ], $this->args);

            
            $page = env('PDF_PAGES', '');
            if ($page) {
                $payload['page'] = $page;
            }

            Log::info('- Send data: ' . print_r($payload, true));

            //15 minutes
           
            $response = sendCurlRequest($pdfTranslationServiceUrl, $payload);

            if (!$response) {
                Log::error("message: Error when send file to API");
                return;
            } 
          
            Log::info(print_r( $response, true));

            $pdfFile = $this->pdfFile;
           

            $translations = $pdfFile->translations;

            \Log::info(print_r( $translations, true));

            $language = $this->inspectionFile->language;
            $translated_file = $response['translated_file'] ?? '';

            \Log::info(print_r( $translated_file, true));

            if (!$translated_file) {
                \Log::error("message: Error when send file to API");
                return;
            }

           

      
            $translation = $translations->where('language', $language)->first();

            if ($translation) {
                $translation->translated_file_path = $translated_file;
                $translation->status = InspectionFileStatus::COMPLETED;
                $translation->save();
            } else {
                $translation = Translation::create([
                    'pdf_file_id' => $pdfFile->id,
                    'language' => $language,
                    'two_page_path' => '',
                    'translated_file_path' => $translated_file,
                    'status' => InspectionFileStatus::COMPLETED,
                ]);
            }

            UploadFileToS3Job::dispatch($translated_file, $this->pdfFile->name, $translation->id);

            $this->inspectionFile->status = InspectionFileStatus::COMPLETED;
            $this->inspectionFile->translation_id = $translation->id;
            $this->inspectionFile->save();

            $user = $pdfFile->user;

            AdminNotification::query()->create([
                'title' => 'Pdf translation completed',
                'action_label' => 'Pdf translation completed',
                'action_url' => '#',
                'description' => 'Pdf translation completed',
            ]);


            if ($user && $user->email) {
                $this->toMail($user);
            }
            
        } catch (\Exception $e) {
            Log::error('Lỗi khi gửi file PDF qua API: ' . $e->getMessage());
        }

        
        Log::info("End send file to API: " . $this->pdfFile->name);

    }

    public function toMail(User $user)
    {
        if (!$user || !$user->email) {
            Log::error("Member: " . $user->name . ' doesn\'t have email');
            return;
        }

        $pdfLinks = '<ul>';
        // $languagesMapping = [
        //     'vi' => 'Vietnamese',
        //     'zh' => 'Chinese',
        //     'es' => 'Spanishese',
        // ];
        //foreach ($this->pdfFile->translated_files as $key => $file) {
            $pdfLinks .= '<strong>['. $this->pdfFile->translated_file .']: </strong><a href="' . $this->pdfFile->translated_file . '" target="_blank">link</a>';
        //}
        $pdfLinks .=  '</ul>';

        try {
            $emailHandler = EmailHandler::setModule(USER_MODULE_SCREEN_NAME)
            ->setType('plugins')
            ->setTemplate('pdf-translation-completed')
            ->addTemplateSettings(INSPECTION_MODULE_SCREEN_NAME, config('plugins.inspection.email', []))
            ->setVariableValues([
                // 'verify_link' =>  env('WEB_URL') . '/inspection-files/' . $this->pdfFile->id,
                'detail_url' => env('WEB_URL') . '/inspection-files/' . $this->pdfFile->id,
                'member_name' => $user->first_name,
                'pdf_links' => $pdfLinks
            ]);

        EmailHandler::send(
            $emailHandler->getContent(),
            $emailHandler->getSubject(),
            $user->email,
        );
        } catch (\Exception $e) {
            Log::error('Error send email: ' . $e->getMessage());
            return;
        }
    }


    public function failed()
    {
       
    }
}

