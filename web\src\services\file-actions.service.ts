import { Dispatch } from 'redux';
import { fetchError, fetchStart, fetchSuccess } from 'redux/actions/Common';
import jwtAxios from '@crema/services/auth/jwt-auth';

export interface ShareFileOptions {
  fileUrl: string;
  recipientEmail: string;
  message?: string;
  fileId?: number;
  fileType?: string;
}

export interface ShareFileResponse {
  success: boolean;
  message: string;
}

/**
 * Share a file via email
 */
export const shareFile = async (
  dispatch: Dispatch<any>,
  options: ShareFileOptions
): Promise<ShareFileResponse> => {
  dispatch(fetchStart());
  
  try {
    const response = await jwtAxios.post('share-file', {
      file_url: options.fileUrl,
      recipient_email: options.recipientEmail,
      message: options.message,
      file_id: options.fileId,
      file_type: options.fileType || 'inspection',
    });
    
    dispatch(fetchSuccess());
    
    if (response.status === 200) {
      return {
        success: true,
        message: response.data.message || 'File shared successfully',
      };
    } else {
      dispatch(fetchError('Failed to share file'));
      return {
        success: false,
        message: 'Failed to share file',
      };
    }
  } catch (error: any) {
    dispatch(fetchError(error.message || 'Failed to share file'));
    return {
      success: false,
      message: error.message || 'Failed to share file',
    };
  }
};

/**
 * Print a file
 * @param fileUrl URL of the file to print
 * @returns Promise that resolves when print dialog is opened
 */
export const printFile = (fileUrl: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const printWindow = window.open(fileUrl, '_blank');
    
    if (!printWindow) {
      resolve(false);
      return;
    }
    
    printWindow.addEventListener('load', () => {
      try {
        printWindow.print();
        resolve(true);
      } catch (error) {
        console.error('Print error:', error);
        resolve(false);
      }
    });
    
    // Fallback in case load event doesn't fire
    setTimeout(() => {
      try {
        printWindow.print();
        resolve(true);
      } catch (error) {
        console.error('Print error (timeout):', error);
        resolve(false);
      }
    }, 3000);
  });
};

/**
 * Copy text to clipboard
 * @param text Text to copy
 * @returns Promise that resolves when text is copied
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Clipboard error:', error);
    return false;
  }
};

/**
 * Download a file
 * @param fileUrl URL of the file to download
 * @param fileName Optional filename for the download
 */
export const downloadFile = (fileUrl: string, fileName?: string): void => {
  const link = document.createElement('a');
  link.href = fileUrl;
  
  if (fileName) {
    link.download = fileName;
  }
  
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};