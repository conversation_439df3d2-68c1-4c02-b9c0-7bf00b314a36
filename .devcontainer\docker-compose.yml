version: '3'
services:
  dashboard:
    image: ghcr.io/archielite/laravel:php8.2
    ports:
      - 9000:80
    volumes:
      - ../api:/var/www/html
      - ./custom.ini:/etc/php82/conf.d/custom.ini
      - ../api/supervisor:/etc/supervisor/conf.d
    environment:
      - NGINX_CLIENT_MAX_BODY_SIZE=200M
      - NGINX_FASTCGI_READ_TIMEOUT=600
    networks:
      - shared-network
    # command: >
    #   bash -c "supervisord -c /etc/supervisor/supervisord.conf && php-fpm"
  # mysql:
  #   image: 'mysql/mysql-server:8.0'
  #   ports:
  #       - '${FORWARD_DB_PORT:-3306}:3306'
  #   environment:
  #       MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
  #       MYSQL_ROOT_HOST: "%"
  #       MYSQL_DATABASE: '${DB_DATABASE}'
  #       MYSQL_USER: '${DB_USERNAME}'
  #       MYSQL_PASSWORD: '${DB_PASSWORD}'
  #       MYSQL_ALLOW_EMPTY_PASSWORD: 1
  #   volumes:
  #       - './data:/var/lib/mysql'
  #   healthcheck:
  #       test: ["CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}"]
  #       retries: 3
  #       timeout: 5s      

networks:
  shared-network:
    external: true
    name: shared-network