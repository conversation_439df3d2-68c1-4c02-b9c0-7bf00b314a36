import React from 'react';
import Grid from '@mui/material/Grid/index';
import StatsCard from './StatsCard';

import Box from '@mui/material/Box';
import {Fonts} from '../../../../shared/constants/AppEnums';
import {QuickStatsData} from 'types/models/dashboards/CRM';

interface QuickStatsProps {
  quickStatsData: QuickStatsData;
}

const QuickStats: React.FC<QuickStatsProps> = ({quickStatsData}) => {
  return (
    <>
      <Box
        component='h2'
        sx={{
          color: 'text.primary',
          fontSize: 16,
          mb: {xs: 4, sm: 4, xl: 6},
          fontWeight: Fonts.BOLD,
          textTransform: 'uppercase',
        }}
      >
        quickStats
      </Box>
      <Grid container spacing={{xs: 4, md: 8}}>
        <Grid item xs={12} sm={6}>
          <StatsCard
            icon={'/assets/images/dashboard/total-clients.svg'}
            data={quickStatsData.member_total}
            heading={'Total members'}
            href='/members'
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <StatsCard
            icon={'/assets/images/dashboard/invoices.svg'}
            data={quickStatsData.transaction_amount}
            heading={'Paid Invoices'}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <StatsCard
            icon={'/assets/images/dashboard/total-projects.svg'}
            data={quickStatsData.translation_total}
            heading={'Total Inspections'}
            href='/inspection-files'
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <StatsCard
            icon={'/assets/images/dashboard/open-projects.svg'}
            data={quickStatsData.inspection_file_total}
            heading={'Open Projects'}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default QuickStats;
