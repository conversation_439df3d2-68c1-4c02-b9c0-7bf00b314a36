import React, { useEffect, useState } from 'react';
import { FormControl, InputLabel, MenuItem, Select, SelectChangeEvent } from '@mui/material';

interface StatusSelectProps {
  onChange: (status: string) => void;
  placeholder?: string;
  value?: string;
}

const StatusSelect: React.FC<StatusSelectProps> = ({ onChange, placeholder = 'Filter by status', value = '' }) => {
  const [selectedStatus, setSelectedStatus] = useState<string>(value);

  useEffect(() => {
    // Set the selected status when the value prop changes
    if (value !== undefined) {
      setSelectedStatus(value);
    }
  }, [value]);

  const handleChange = (event: SelectChangeEvent<string>) => {
    const status = event.target.value;
    setSelectedStatus(status);
    onChange(status);
  };

  return (
    <FormControl sx={{ minWidth: 200 }}>
      <InputLabel id="status-select-label">{placeholder}</InputLabel>
      <Select
        labelId="status-select-label"
        id="status-select"
        value={selectedStatus}
        label={placeholder}
        onChange={handleChange}
        displayEmpty
      >
        <MenuItem value="">
          <em>All statuses</em>
        </MenuItem>
        <MenuItem value="pending">Pending</MenuItem>
        <MenuItem value="inprogress">In Progress</MenuItem>
        <MenuItem value="completed">Completed</MenuItem>
        <MenuItem value="rejected">Rejected</MenuItem>
      </Select>
    </FormControl>
  );
};

export default StatusSelect;

