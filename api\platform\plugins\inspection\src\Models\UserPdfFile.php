<?php

namespace Bo<PERSON>ble\Inspection\Models;

use Bo<PERSON>ble\ACL\Models\User;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Inspection\Enums\PaymentStatus;
use Bo<PERSON>ble\Inspection\Enums\TransactionStatus;

class UserPdfFile extends BaseModel
{

    protected $table = 'user_pdf_file';
    //const $payment_status = ['pending', 'paid', 'failed'];

    protected $fillable = [
        //user_id, pdf_file_id,  language=> unique
        'user_id', 
        'pdf_file_id', 
        'language',

        'translation_id',  // pdf_file_id + lang: belong to translation_id
        'preview_file_path',  // pdf_file_id + lang: belong to translation_id
        'company_id', //user belong to company
        'status',
        'created_at', 
    
        'payment_status',
        'transaction_id',
        'price',
        'envelope_id',
        'signed_document'
    ];

    protected $casts = [
        'payment_status' => PaymentStatus::class, 
    ];


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function pdfFile()
    {
        return $this->belongsTo(PdfFile::class);
    }

     public function translation()
    {
        return $this->belongsTo(Translation::class, 'translation_id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }
}