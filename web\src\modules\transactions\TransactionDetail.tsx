import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { 
  Box, 
  Card, 
  CardContent, 
  CircularProgress, 
  Divider, 
  Grid, 
  Paper, 
  Typography,
  Chip,
  Button
} from '@mui/material';
import { Transaction, getTransactionById } from 'services/transaction.service';
import Link from 'next/link';
import { ArrowBack, Receipt, Person, Description } from '@mui/icons-material';

const TransactionDetail = () => {
  const router = useRouter();
  const { id } = router.query;
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTransaction = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const response = await getTransactionById(id as string);
        setTransaction(response.data);
      } catch (err) {
        console.error('Error fetching transaction:', err);
        setError('Failed to load transaction details');
      } finally {
        setLoading(false);
      }
    };

    fetchTransaction();
  }, [id]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', p: 3 }}>
        <Typography color="error" variant="h6">{error}</Typography>
        <Button 
          variant="contained" 
          sx={{ mt: 2 }} 
          onClick={() => router.push('/transactions')}
        >
          Back to Transactions
        </Button>
      </Box>
    );
  }

  if (!transaction) {
    return (
      <Box sx={{ textAlign: 'center', p: 3 }}>
        <Typography variant="h6">Transaction not found</Typography>
        <Button 
          variant="contained" 
          sx={{ mt: 2 }} 
          onClick={() => router.push('/transactions')}
        >
          Back to Transactions
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ mx: 'auto', p: 2, width: '100%' }}>

      <Paper elevation={2} sx={{ mb: 4, overflow: 'hidden' }}>
        <Box sx={{ p: 3, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
          <Typography variant="h6">Transaction #{transaction.id}</Typography>
          <Chip 
            label={transaction.status} 
            color={getStatusColor(transaction.status) as any}
            size="small"
            sx={{ mt: 1 }}
          />
        </Box>
        
        <Grid container spacing={3} sx={{ p: 3 }}>
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Receipt sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Transaction Information</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">Transaction ID</Typography>
                  <Typography>{transaction.id}</Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">Amount</Typography>
                  <Typography>${transaction.amount.toFixed(2)}</Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">Inspection file</Typography>
                  <Typography>${transaction.inspection_file?.name}</Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">Created at</Typography>
                  <Typography>${transaction.created_at}</Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                  <Chip 
                    label={transaction.status} 
                    color={getStatusColor(transaction.status) as any}
                    size="small"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Person sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">User Information</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />
                
                {transaction.user && (
                  <>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">User ID</Typography>
                      <Typography>{transaction.user.id}</Typography>
                    </Box>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Name</Typography>
                      <Typography>
                        {transaction.user.first_name} {transaction.user.last_name}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                      <Typography>{transaction.user.email}</Typography>
                    </Box>
                    
                    {/* <Button 
                      variant="outlined" 
                      size="small"
                      component={Link}
                      href={`/members/${transaction.user.id}`}
                    >
                      View User Profile
                    </Button> */}
                    <Link href={`/members/${transaction.user.id}`}>
                      View User Profile
                    </Link>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
          
          {transaction.inspection_file && (
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Description sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">Inspection File</Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">File ID</Typography>
                    <Typography>{transaction.inspection_file.id}</Typography>
                  </Box>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Name</Typography>
                    <Typography>{transaction.inspection_file.name}</Typography>
                  </Box>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                    <Chip 
                      label={transaction.inspection_file.status} 
                      color={getStatusColor(transaction.inspection_file.status) as any}
                      size="small"
                    />
                  </Box>
                  
                  <Button 
                    variant="contained" 
                    size="small"
                    component={Link}
                    href={`/inspection-files/${transaction.inspection_file.id}`}
                  >
                    View Inspection File
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </Paper>
    </Box>
  );
};

export default TransactionDetail;