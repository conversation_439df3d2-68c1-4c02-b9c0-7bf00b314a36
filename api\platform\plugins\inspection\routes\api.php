<?php

use Bo<PERSON><PERSON>\Inspection\Http\Controllers\API\StripeWebhookController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => 'api',
    'prefix' => 'api/v1',
    'as' => 'api.'
], function (): void {
    /**
     * Anonymous routes for members
     */
    Route::group(['prefix' => 'members', 'as' => 'member.', 'namespace' => 'Botble\Inspection\Http\Controllers\API'], function (): void {
        Route::post('login', [
            'uses' => 'LoginController@login',
        ]);

        Route::post('register', [
            'uses' => 'UserController@store',
        ]);

        
      
    });

      
        // DocuSign routes
        // Route::group(['prefix' => 'docusign', 'namespace' => 'Botble\Inspection\Http\Controllers\API'], function (): void {
        //     Route::post('create-envelope', 'DocusignController@createEnvelope');
        //     Route::get('envelope/{envelopeId}/status', 'DocusignController@getEnvelopeStatus');
        //     Route::post('create-signing-url', 'DocusignController@createSigningUrl');
        //     Route::get('envelope/{envelopeId}/download', 'DocusignController@downloadSignedDocument');
        //     Route::get('webhook', 'DocusignController@handleWebhook');
        // });

       
    /**
     * Authenticated routes for members
     */
    Route::group([
        'middleware' => ['auth:sanctum', 'resolve.context'],
        'namespace' => 'Botble\Inspection\Http\Controllers\API', 
    ], function (): void {

        Route::group(['prefix' => 'notifications'], function (): void {
            generateApiResourceRoutes('NotificationController', '');
        });

        Route::group(['prefix' => 'coupons'], function (): void {
            generateApiResourceRoutes('CouponController', '');
        });

        Route::group(['prefix' => 'transactions'], function (): void {
            generateApiResourceRoutes('TransactionController', '');
            Route::post('/callback', [StripeWebhookController::class, 'handle']);
        });

        Route::group(['prefix' => 'inspection-files'], function (): void {
            generateApiResourceRoutes('PdfFileController', '');

            Route::get('{id}/translation-preview', 'PdfFileController@translationPreview');
            Route::get('{id}/translate', 'PdfFileController@translate');
            Route::get('{pdfFileId}/pdf-info', 'PdfFileController@getPageInfo');

            Route::post('payment/{id}/status', 'PdfFileController@updatePaymentStatus');

            Route::get('{id}/tranlations', 'PdfFileController@getTranslations');
        });

        Route::group(['prefix' => 'companies'], function (): void {
            generateApiResourceRoutes('CompanyController', '');
        });

        Route::group(['prefix' => 'translations'], function (): void {
            generateApiResourceRoutes('TranslationController', '');
        });

        Route::group(['prefix' => 'users'], function (): void {
            generateApiResourceRoutes('UserController', '');
            Route::put('{id}/change-password', 'UserController@changePassword');
            Route::get('{id}/inspection-files', 'UserController@inspectionFiles');
            Route::get('{id}/translations', 'UserController@transactions');
        });

        Route::get('auth', [
            'uses' => 'UserController@getCurrentMember',
        ]);

        Route::group(['prefix' => 'report'], function (): void {
            Route::get('statistics', 'ReportController@statistics');
        });

        Route::group(['prefix' => 'settings'], function (): void {
            Route::get('', 'SettingController@getGeneralSetting');
            Route::post('', 'SettingController@update');

            Route::get('docusign', 'SettingController@docusign');
            Route::post('docusign', 'SettingController@docusignUpdate');

            Route::get('stripe', 'SettingController@stripe');
            Route::post('stripe', 'SettingController@stripeUpdate');
        });

        Route::group(['prefix' => 'email-templates'], function (): void {
            Route::get('', 'EmailTemplateController@index');
            Route::post('', 'EmailTemplateController@update');
        });

        Route::group(['prefix' => 'webhooks'], function (): void {
            Route::post('translation-completed', 'TranslationWebhookController@handle');
        });
        
        // DocuSign routes
        Route::group(['prefix' => 'docusign'], function (): void {
            Route::post('create-envelope', 'DocusignController@createEnvelope');
            Route::get('envelope/{envelopeId}/status', 'DocusignController@getEnvelopeStatus');
            Route::post('create-signing-url', 'DocusignController@createSigningUrl');
            Route::get('envelope/{envelopeId}/download', 'DocusignController@downloadSignedDocument');
            Route::get('webhook', 'DocusignController@handleWebhook');
        });
    });
});

