import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField, CircularProgress, Typography, Box, Snackbar, Alert, ButtonProps } from '@mui/material';
import { useDispatch } from 'react-redux';
import { createEnvelope, createSigningUrl } from 'services/docusign.service';

export interface DocuSignButtonProps {
  pdfFileId: number;
  fileName: string;
  buttonProps?: ButtonProps;
  buttonText?: string;
  showButton?: boolean;
  onSuccess?: (envelopeId: string) => void;
  onError?: (message: string) => void;
}

export interface DocuSignButtonRef {
  openDialog: () => void;
}

const DocuSignButton = forwardRef<DocuSignButtonRef, DocuSignButtonProps>(({ 
  pdfFileId, 
  fileName, 
  buttonProps = {}, 
  buttonText = 'Sign with DocuSign',
  showButton = true,
  onSuccess,
  onError
}, ref) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [signerEmail, setSignerEmail] = useState('');
  const [signerName, setSignerName] = useState('');
  const [emailError, setEmailError] = useState('');
  const [nameError, setNameError] = useState('');
  
  // Add local snackbar state
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');

  // Expose the openDialog method to parent components
  useImperativeHandle(ref, () => ({
    openDialog: () => {
      setOpen(true);
    }
  }));

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSignerEmail('');
    setSignerName('');
    setEmailError('');
    setNameError('');
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Show message function
  const showMessage = (message: string, severity: 'success' | 'error' | 'info' | 'warning' = 'info') => {
    if (severity === 'error' && onError) {
      onError(message);
    } else {
      setSnackbarMessage(message);
      setSnackbarSeverity(severity);
      setSnackbarOpen(true);
    }
  };

  const validateForm = () => {
    let isValid = true;
    
    if (!signerEmail) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(signerEmail)) {
      setEmailError('Email is invalid');
      isValid = false;
    } else {
      setEmailError('');
    }

    if (!signerName) {
      setNameError('Name is required');
      isValid = false;
    } else {
      setNameError('');
    }

    return isValid;
  };

  const handleSendForSignature = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      // Step 1: Create envelope
      const envelopeResponse = await createEnvelope(dispatch, {
        pdf_file_id: pdfFileId,
        signer_email: signerEmail,
        signer_name: signerName
      });
      
      if (!envelopeResponse) {
        showMessage('Failed to create envelope', 'error');
        setLoading(false);
        return;
      }
      
      // Step 2: Create signing URL
      const returnUrl = `${window.location.origin}/inspection-files/${pdfFileId}?envelope_id=${envelopeResponse.envelope_id}`;
      
      const signingUrlResponse = await createSigningUrl(dispatch, {
        envelope_id: envelopeResponse.envelope_id,
        return_url: returnUrl
      });
      
      if (!signingUrlResponse) {
        showMessage('Failed to create signing URL', 'error');
        setLoading(false);
        return;
      }
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(envelopeResponse.envelope_id);
      }
      
      // Step 3: Redirect to signing URL
      window.location.href = signingUrlResponse.signing_url;
      
    } catch (error) {
      console.error('DocuSign error:', error);
      showMessage('An error occurred during the DocuSign process', 'error');
    } finally {
      setLoading(false);
      handleClose();
    }
  };

  return (
    <>
      {showButton && (
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleOpen}
          startIcon={<img src="/assets/images/docusign-logo.svg" alt="DocuSign" width={20} height={20} />}
          {...buttonProps}
        >
          {buttonText}
        </Button>
      )}
      
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>Send Document for Signature</DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Send "{fileName}" for electronic signature via DocuSign.
          </Typography>
          
          <TextField
            autoFocus
            margin="dense"
            id="signer-email"
            label="Signer Email"
            type="email"
            fullWidth
            value={signerEmail}
            onChange={(e) => setSignerEmail(e.target.value)}
            error={!!emailError}
            helperText={emailError}
            sx={{ mb: 2 }}
          />
          
          <TextField
            margin="dense"
            id="signer-name"
            label="Signer Name"
            type="text"
            fullWidth
            value={signerName}
            onChange={(e) => setSignerName(e.target.value)}
            error={!!nameError}
            helperText={nameError}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSendForSignature} 
            color="primary" 
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            Send for Signature
          </Button>
        </DialogActions>
      </Dialog>

      {/* Local Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
});

DocuSignButton.displayName = 'DocuSignButton';

export default DocuSignButton;
