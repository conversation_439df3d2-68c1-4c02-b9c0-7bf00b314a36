<?php

namespace Bo<PERSON>ble\Inspection\Http\Controllers\API;

use App\Jobs\ExampleJob;
use App\Jobs\ProcessTranslatePdfService;
use App\Models\User;
use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Inspection\Enums\InspectionFileStatus;
use Botble\Inspection\Enums\PaymentStatus;
use Botble\Inspection\Enums\TransactionStatus;
use Illuminate\Support\Facades\Log;
use Botble\Inspection\Models\PdfFile;
use Botble\Inspection\Models\Transaction;
use Botble\Inspection\Models\Translation;
use Botble\Inspection\Models\UserPdfFile;
use Botble\Inspection\Repositories\Interfaces\PdfFileInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;


class PdfFileController extends BaseController
{
    protected PdfFileInterface $pdfFileRepository;

    public function __construct(PdfFileInterface $pdfFileRepository)
    {
        $this->pdfFileRepository = $pdfFileRepository;
    }

    public function index(Request $request)
    {
        $currentPage = $request->input('page', 1);
        $memberId = $request->input('member_id');
        $status = $request->input('status');
        $perPage = $request->input('per_page', 10);

        $currentUser = Auth::guard()->user();

        $token = $currentUser->currentAccessToken();
        $companyId = $token->company_id ?? config('constants.COMPANY_DEFAULT');
        $role = $token->role;

        $search = $request->input('search', '');
        $search = $request->input('search', '');

        /**
         * moi them translation, cần check pdfFile.translations: dư
         */
        $query = UserPdfFile::with(['user', 'pdfFile.translations', 'translation'])
            ->orderByDesc('created_at');

        if ($role === 'member') {
            $query->where('user_id', $currentUser->id);
        }

        if ($role === 'admin') {
            $query->where('company_id', $companyId);
        }

        $search = trim($search || '');

        if (!empty($search)) {
            $query->whereHas('pdfFile', function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%");
            });
        }

        if (!empty($memberId)) {
            $query->where('user_id', $memberId);
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        $pdfFiles = $query->paginate($perPage, ['*'], 'page', $currentPage);
        
        // $transformed = $pdfFiles->getCollection()->map(function ($item) {
         
        //     $data = $item->toArray(); 
        //     $data['md5'] = $item->md5;
        
        //     return $data;
        // });

        // $pdfFiles->setCollection(collect($transformed));
        

        return $this
            ->httpResponse()
            ->setData($pdfFiles)
            ->toApiResponse();
    }

    public function store(Request $request)
    {
        $token = auth()->user()?->currentAccessToken();
        $companyId = $token->company_id ?? config('constants.COMPANY_DEFAULT');

        $request->validate([
            'source_file' => 'required|file|mimes:pdf,doc,docx,txt',
        ]);

        $file = $request->file('source_file');
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();
        $md5 = md5_file($file->getRealPath());
        $fileName = $originalName . '_' . $md5 . '.' . $extension;
        $s3Path = 'uploads/original/' . $fileName;

        try {
            $stream = fopen($file->getRealPath(), 'r');
            $fileSize = $file->getSize();
            $curentUser = auth()->user();

            $pdfFile = $this->pdfFileRepository->getByMd5($md5);
            if (!$pdfFile) {
                Storage::disk('contabo')->put($s3Path, $stream, [
                    'visibility' => 'public'
                ]);
                $publicUrl = Storage::disk('contabo')->url($s3Path);

                $pdfFile = PdfFile::forceCreate([
                    'source_file' => $publicUrl, 
                    'name' => $fileName,
                    'md5' => $md5,
                    'file_size' => $fileSize,
                    // 'languages' => $request->input('languages', 'vi'),
                    // 'company_id' => $companyId,
                    'upload_by' => $curentUser->id,
                    // 'status' => InspectionFileStatus::PENDING,
                ]);
            }

            fclose($stream);

            $data = UserPdfFile::query()->create([
                'user_id' => $curentUser->id,
                'pdf_file_id' => $pdfFile->id,
                'language' => $request->input('language', 'vi'),
                'company_id' => $companyId,
                'status' => InspectionFileStatus::PENDING,
                'payment_status' => PaymentStatus::UNPAID,
            ]);
       
            // $this->translate($pdfFile, $request->input('languages', 'vi'));
            //$this->translationPreview($request);
    
            return $this->httpResponse()
                ->setData($data->toArray())
                ->toApiResponse();
    
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Lỗi khi upload hoặc lưu file',
                'details' => $e->getMessage(),
            ], 500);
        }
    }

    public function translationPreview($inspectionId)
    {
        $inspectionFile = UserPdfFile::query()
            ->with('pdfFile')
            ->where('id', $inspectionId)->firstOrFail();

        $pdfFile = $inspectionFile->pdfFile;
        
        /**
         * validate
         */
        if ($pdfFile->page_count < 10) {
            return $this->httpResponse()
            ->setError()
            ->setMessage('This feature is available for files with at least 5 pages')
            ->toApiResponse();
        }

        $pdfFile = $inspectionFile->pdfFile;
        $translations = $pdfFile->translations;
        $sourceFile = $inspectionFile->pdfFile->source_file;
        $language =  $inspectionFile->language;


        $payload = [
            "language" => $language,
            // "path" => $source_file,
            "pdf_url" => $sourceFile,
            "trim_out_of_range" => true,
            "page_from" => 1,
            "page_to" => 2,
        ];

        Log::info(print_r($payload, true));

        $pdfTranslationServiceUrl = env('PDF_TRANSLATION_SERVICE_URL') . '/translate';
        $response = sendCurlRequest($pdfTranslationServiceUrl, $payload);

        if (!$response) {
            Log::error("message: Error when send file to API");
            return null;
        } 

        $inspectionFile->preview_file_path = $response['translated_file'];
        $inspectionFile->save();

        $exist = $translations->where('language', $language)->first();

        if ($exist) {
            $exist->two_page_path = $response['translated_file'];
            $exist->save();
        } else {
            Translation::query()->create([
                'pdf_file_id' => $pdfFile->id,
                'language' => $language,
                'two_page_path' => $response['translated_file'],
                'translated_file_path' => null,
                'status' => InspectionFileStatus::PENDING,
            ]);
        }

        return $this->httpResponse()
        ->setData($inspectionFile->toArray())
        ->toApiResponse();
    }

    public function getPageInfo($pdfFileId)
    {
        $pdfFile = PdfFile::query()->find($pdfFileId);

        if ($pdfFile->page_count) {
            return $this->httpResponse()
            ->setData([
                'page_count' => $pdfFile->page_count
            ])
            ->toApiResponse();
        }

        $sourceFile = $pdfFile->source_file;

        \Log::info($pdfFileId);
        \Log::info(print_r( $sourceFile, true));

        $pdfTranslationServiceUrl = env('PDF_TRANSLATION_SERVICE_URL') . '/file-info?pdf_url=' . $sourceFile;
        $result = sendCurlRequest($pdfTranslationServiceUrl);

        if ($result['page_count']) {
            $pdfFile->page_count = $result['page_count'];
            $pdfFile->save();
        }

        return $this->httpResponse()
        ->setData([
            'page_count' => $result['page_count'] ?? 0
        ])
        ->toApiResponse();
    }

    public function updatePaymentStatus($inspectionId, Request $request)
    {
        $inspectionFile = UserPdfFile::query()
        ->with('pdfFile')
        ->where('id', $inspectionId)->firstOrFail();

        $inspectionFile->payment_status = PaymentStatus::PAID;
        $inspectionFile->save();

        return $this->httpResponse()
        ->setData($inspectionFile->toArray())
        ->toApiResponse();
    }


    public function translate($inspectionId)
    {
        $inspectionFile = UserPdfFile::query()
        ->with('pdfFile', 'translation')
        ->where('id', $inspectionId)->firstOrFail();

        if ($inspectionFile->payment_status !== PaymentStatus::PAID) {
            $pdfFile = $inspectionFile->pdfFile;
            $pricePerPage = 3;
            if (!$inspectionFile->price) {
                $inspectionFile->price = $pdfFile->page_count * $pricePerPage;
                $inspectionFile->save();
            }
            

            $transaction = Transaction::query()
            ->create([
                'amount' => $inspectionFile->price,
                'user_id' => $inspectionFile->user_id, 
                'status' => TransactionStatus::PENDING,
                'company_id' => $inspectionFile->company_id,
                'user_pdf_file_id' => $inspectionId 
            ]);

            /**
             * Create transaction if it has not been created
             */
            return $this->httpResponse()
                ->setError(true)
                // ->setCode(402)
                ->setMessage('Payment required')
                ->setData([
                    'redirect_url' => 'https://stripe.com/',   //update STRIPE payment url here
                    'callback_url' => env('WEB_URL') . '/transactions/' .  $transaction->id . '/callback', 
                    'transaction_id' => $transaction->id,  
                ])
                ->toApiResponse();
        }

        // $pdfFile =  $inspectionFile->pdfFile;

        $sourceFile = $inspectionFile->pdfFile->source_file;

        $language = $inspectionFile->language;

        $payload = [
            "language" => $language,
            "pdf_url" => $sourceFile,
            "page_from" => 1,  //for test
            "page_to" => 2,  //for test
            'trim_out_of_range' => false,
        ];

        $inspectionFile->status = InspectionFileStatus::IN_PROGRESS;
        $inspectionFile->save();

        ProcessTranslatePdfService::dispatch($inspectionFile, $payload);

        /**
         * Now translate implediately
         */

        $inspectionFile = UserPdfFile::query()
        ->with('pdfFile', 'translation')
        ->where('id', $inspectionId)->firstOrFail();

        return $this->httpResponse()
        ->setData($inspectionFile->toArray())
        ->toApiResponse();
    }

    public function getPublicFile($path) 
    {
        $pdfTranslationUrl = env('PDF_TRANSLATION_SERVICE_URL') ;
        $path = str_replace('/app/storage', '/files', $path);
        return $pdfTranslationUrl. $path;
    }
   
    public function show($id)
    {
        $pdfFile = UserPdfFile::query()
        ->with('user', 'pdfFile', 'translation')
        ->where('id', $id)->firstOrFail();

        // ExampleJob::dispatch()->onQueue('high');

        return $this
            ->httpResponse()
            ->setData($pdfFile)
            ->toApiResponse();
    }

    public function getByMd5(string $md5)
    {
        $pdfFile = $this->pdfFileRepository->getByMd5($md5);

        $pdfFile->translations = $pdfFile->translations->transform(function ($translation) {
            return [
                'id' => $translation->id,
                'translated_file' => $this->getPublicFile($translation->full_translated_pdf_path),
                'full_translated_pdf_path' => $translation->full_translated_pdf_path,
                'lang' => $translation->lang
            ];
        });

        return $this
            ->httpResponse()
            ->setData($pdfFile)
            ->toApiResponse();
    }

    // public function edit(int $id)
    // {
    //     $pdfFile = $this->pdfFileRepository
    //     ->getModel()
    //     ->where('id', $id)
    //     ->with('translations')
    //     ->first();
    //     return $this
    //         ->httpResponse()
    //         ->setData($pdfFile)
    //         ->toApiResponse();
    // }

    public function update(PdfFile $pdfFile, Request $request)
    {
        PdfFile::where('id', $pdfFile->id)->update($request->all());
        return $this
            ->httpResponse()
            ->setData([
                'message' => 'Update inspectionFile successfully'
            ])
            ->toApiResponse();
    }

    public function destroy(int $id)
    {
        PdfFile::destroy($id);
        return $this
            ->httpResponse()
            ->setData([
                'message' => 'Delete inspectionFile successfully'
            ])
            ->toApiResponse();
    }

    public function getTranslations($id)
    {
        $translations = Translation::query()
            ->orderBy('id', 'DESC')
            ->where('pdf_file_id', $id)
            ->get();
       
   
        return $this
            ->httpResponse()
            ->setData($translations)
            ->toApiResponse();
    }
    

}
