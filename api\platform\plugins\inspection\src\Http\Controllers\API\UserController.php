<?php

namespace Bo<PERSON>ble\Inspection\Http\Controllers\API;

use Bo<PERSON>ble\ACL\Forms\PasswordForm;
use <PERSON><PERSON>ble\ACL\Models\User;
use <PERSON><PERSON>ble\ACL\Repositories\Interfaces\UserInterface;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Inspection\Models\Company;
use Botble\Inspection\Models\Transaction;
use Botble\Inspection\Models\UserPdfFile;
use Botble\Inspection\Services\ChangePasswordService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\HasApiTokens;
use Throwable;

class UserController extends BaseController
{
    protected UserInterface $userRepository;

    public function __construct(UserInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getCurrentMember(Request $request)
    {
        $user = $request->user();
        $userRoles = $user->companyRoles;
        if ($userRoles->isEmpty()) {
            return $this->httpResponse()
                ->setError(true)
                ->setMessage('You do not have any company role assigned.')
                ->toApiResponse();
        }
       
        $userRoleSelected = $userRoles->first();
        
        $token = $user->createTokenWithContext('auth_token',  $userRoleSelected ? $userRoleSelected->company_id : null, $userRoleSelected ? $userRoleSelected->role : null)->plainTextToken;

        $token = $user->currentAccessToken();
        $companyId = $token->company_id;
        $role = $token->role;

        return $this
            ->httpResponse()
            ->setData([
                'user'  => $user,
                'token' => $token,
                'company_id' => $companyId,
                'super_user' => $user->isSuperUser(),
                'role' => $role,
                'user_roles' => $userRoles->map(function ($role) {
                    return [
                        'company_id' => $role->company_id,
                        'role' => $role->role,
                    ];
                }),
            ])
            ->toApiResponse();
    }

    public function index(Request $request)
    {
        $currentPage = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $search  = $request->input('search', '');
        $token = auth()->user()->currentAccessToken();
        $companyId = $token->company_id ?? COMPANY_DEFAULT;

        $query = $this->userRepository->getModel()
                ->whereHas('companyRoles', function ($q) use ($companyId) {
                    $q->where('company_id', $companyId);
                })
                // ->withCount('inspectionFiles')
                ->with('companyRoles')
                ->orderBy('id', 'DESC');

        $search = trim($search || '');

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('email', 'LIKE', "%{$search}%")
                    ->orwhere('first_name', 'LIKE', "%{$search}%");
            });
        }
        
        $users = $query->paginate($perPage, ['*'], 'page', $currentPage);

        $users->getCollection()->transform(function ($user) {
            return [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
                'role' => $user->companyRoles->first()->role ?? null,
                'is_external' => $user->is_external,
                'last_login' => $user->last_login,
                'super_user' => $user->super_user,
                'status' => $user->status,
                'verified' => true,
                'total_pdf' => $user->total_pdf,
            ];
        });

        return $this
            ->httpResponse()
            ->setData($users)
            ->toApiResponse();
    }

    public function store(Request $request)
    {
        $token = auth()->user()->currentAccessToken();
       
        $companyId = $token->company_id;
        $role = $token->role;

        $request->validate([
            'email' => 'required|email|unique:users',
            // 'first_name' => 'required',
            // 'company_id' => 'required',
            'password' => 'required',
        ]);

        $input = $request->all();
        $user = $this->create($input);

        if (!$companyId) {
            $companyId = $input['company_id'] ?? null;
        }

        if (!$companyId) {
            return $this
                ->httpResponse()
                ->setError(true)
                ->setMessage('Company ID is required.')
                ->toApiResponse();
        }

        $role = $input['role'] ?? 'member';
        $user->companyRoles()->create([
            'company_id' => $companyId,
            'role' => $role,
        ]);

        // Table user not has column confirmed_at
        // $user->confirmed_at = Carbon::now();
        $user->save();

        // $token = $user->createToken('auth_token')->plainTextToken;
        $token = $user->createTokenWithContext('auth_token',  $companyId, $role)->plainTextToken;
        return $this->httpResponse()
            ->setData([
                'token' => $token,
                'member' => $user,
                'company_id' => $companyId,
                'role' => $role,
            ])
            ->toApiResponse();
        
    }

    public function show(Request $request, int $id)
    {
        $companyId = $request->auth_company_id;

        $companies = Company::query()
            ->get();


        $user = $this->userRepository->getModel()
            ->where('id', $id)
            ->firstOrFail();
     
        $userRoles = $user->companyRoles()->where('company_id', $companyId);
        $user->company_role = $userRoles->count() > 0 ? $userRoles->first() : null;
        $user->is_super_user = $user->super_user;

        return $this
            ->httpResponse()
            ->setData([
                'user' => $user,
                'companies' => $companies,
            ])
            ->toApiResponse();
    }

    public function edit(int $id)
    {
        $member = $this->userRepository->findOrFail($id);
        return $this
            ->httpResponse()
            ->setData($member)
            ->toApiResponse();
    }

    public function update(int $id, Request $request)
    {
        $user = User::findOrFail($id);
        $user->update($request->all());

        return $this
            ->httpResponse()
            ->setData([
                'user' => $user,
            ])
            ->toApiResponse();
    }

    public function destroy(int $id)
    {
        User::destroy($id);
        return $this
            ->httpResponse()
            ->setData([
                'message' => 'Delete member successfully'
            ])
            ->toApiResponse();
    }

    public function changePassword(int $id, Request $request, ChangePasswordService $service)
    {
        $request->validate([
            'new_password' => 'required',
            'current_password' => 'required',
        ]);
        
        $request->merge(['id' => $id]);

        try {
            $service->execute($request);            
        } catch (Throwable $exception) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($exception->getMessage());
        }

        return $this
            ->httpResponse()
            ->setMessage("Change password successfully!");
    }

    protected function create(array $data)
    {
        return User::query()->forceCreate([
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ]);
    }

    protected function guard()
    {
        return auth();
    }

    public function inspectionFiles(Request $request, int $id)
    {
        $inspectionFiles = UserPdfFile::query()
            ->with('pdfFile', 'translation')
            ->where('user_id', $id)
            ->get();

        return $this
            ->httpResponse()
            ->setData($inspectionFiles)
            ->toApiResponse();
    }

    public function transactions(Request $request, int $id)
    {
        $transactions = Transaction::query()        
            ->where('user_id', $id)
            ->orderBy('id', 'DESC')
            ->with('userPdfFile')
            ->get();

        return $this
            ->httpResponse()
            ->setData($transactions)
            ->toApiResponse();
    }
}