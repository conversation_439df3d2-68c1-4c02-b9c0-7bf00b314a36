import Settings from './Setting';
import Common from './Common';
import Dashboard, { reportReducer } from './dashboard.reducer';
import InspectionFileApp from './inspection-file.reducer';
import { userReducer, userListReducer } from './user.reducers';
import { companyReducer, companyListReducer } from './company.reducer';

const reducers = {
  settings: Settings,
  common: Common,
  dashboard: Dashboard,
  inspectionFileApp: InspectionFileApp,

  // TODO: combine all reducers
  user: userReducer,
  userList: userListReducer,

  company: companyReducer,
  companyList: companyListReducer,

  report: reportReducer
};

export default reducers;
