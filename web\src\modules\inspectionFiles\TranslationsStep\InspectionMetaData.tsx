import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Typography,
} from '@mui/material';
import { InspectionFile,  getPdfInfo, PdfFileStatus as PdfFileStatusType } from 'services/inspection-file.service';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import LanguageIcon from '@mui/icons-material/Language';
import PdfFileStatus from 'common/PdfFileStatus';
import { Label } from '@mui/icons-material';
import ReceiptIcon from '@mui/icons-material/Receipt';
import PaymentStatus, { PaymentStatusType } from 'common/PaymentStatus';

interface InspectionMetaDataProps {
    inspectionFile: InspectionFile
}

interface PdfInfoProps {
  page_count: number;
}

const InspectionMetaData: React.FC<InspectionMetaDataProps> = ({ inspectionFile }) => {
    const PRICE_PER_PAGE = 10;

    const pdfFile = inspectionFile.pdf_file;
    const [pdfInfo, setPdfInfo] = useState<PdfInfoProps | null>(null);

    useEffect(() => {
        if (inspectionFile.pdf_file.page_count) {
            setPdfInfo({
              page_count: inspectionFile.pdf_file.page_count
            });
          } else {
            handleGetPdfInfo(inspectionFile.pdf_file_id);
          }
    }, [])

      const handleGetPdfInfo = async (pdfFileId) => {
        if (!pdfFileId) {
          return;
        }
    
        const response = await getPdfInfo(pdfFileId as string);
    
        const info = response?.data;
        console.log('info', info)
        setPdfInfo(info)
      }

    return (
        <>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 5 }}>
            <PictureAsPdfIcon sx={{ fontSize: 50, color: '#B17E37' }} />
            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography variant='subtitle2' color='text.secondary' sx={{ fontSize: 18, color: '#000' }}>
                ${pdfFile.name}
            </Typography>
            <Typography variant='subtitle2' color='text.secondary' sx={{ fontSize: 14 }}>
                {`${pdfInfo?.page_count || 'N/A'}`} pages
            </Typography>
            </Box>
        </Box>
        <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
            <LanguageIcon sx={{ fontSize: 14 }} />
            <Typography sx={{ fontSize: 16 }} variant='body1' color='text.secondary'>
            Language:  {`English -> ${inspectionFile.language}`} |  Price: ${`${(pdfInfo?.page_count || 0) * PRICE_PER_PAGE}`}
            </Typography>
        </Box>
        <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
            <Label sx={{ fontSize: 14 }} />
            <Typography sx={{ fontSize: 16 }} variant='body1' color='text.secondary'>
            Status:  {<PdfFileStatus status={inspectionFile.status} size="small" />}
            </Typography>
        </Box>
        <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
            <ReceiptIcon sx={{ fontSize: 14 }} />
            <Typography sx={{ fontSize: 16 }} variant='body1' color='text.secondary'>
              Payment: <PaymentStatus status={inspectionFile.payment_status} size="small" />
              {inspectionFile.payment_status === PaymentStatusType.PAID && (
                <a href={`/transactions/${inspectionFile.transaction_id}`} target='_blank' rel='noopener noreferrer'>
                  | View Invoice
                </a>
              )}
            </Typography>
        </Box>
        </>
    )
}

export default InspectionMetaData;