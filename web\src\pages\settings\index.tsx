import { AppComponentHeader, AppContentView } from '@crema';
import AppPage from '@crema/hoc/AppPage';
import asyncComponent from '@crema/utility/asyncComponent';
import { Box } from '@mui/material';
import React from 'react';

const Settings = asyncComponent(() => import('../../modules/settings/Settings'));

const SettingPage = () => {
  return (
    <>
      <AppComponentHeader 
        title='Settings'
        description='View and manage settings information'
      />
      <Settings />
    </>
  );
};

export default AppPage(() => <SettingPage />);
