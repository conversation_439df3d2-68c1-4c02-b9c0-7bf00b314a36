<?php

namespace Bo<PERSON>ble\Inspection\Http\Controllers\API;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Inspection\Models\UserPdfFile;
use Bo<PERSON>ble\Inspection\Models\PdfFile;
use Bo<PERSON>ble\Setting\Supports\SettingStore;
use DocuSign\eSign\Api\EnvelopesApi;
use DocuSign\eSign\Api\EnvelopesApi\ListDocumentsOptions;
use DocuSign\eSign\Client\ApiClient;
use DocuSign\eSign\Client\ApiException;
use DocuSign\eSign\Configuration;
use DocuSign\eSign\Model\Document;
use DocuSign\eSign\Model\EnvelopeDefinition;
use DocuSign\eSign\Model\Recipients;
use DocuSign\eSign\Model\Signer;
use DocuSign\eSign\Model\SignHere;
use DocuSign\eSign\Model\Tabs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocusignController extends BaseController
{
    protected $apiClient;
    protected $config;
    protected $settingStore;
    protected $accountId;

    public function __construct(SettingStore $settingStore)
    {
        $this->settingStore = $settingStore;
        $this->initializeDocusignClient();
        $this->accountId = config('services.docusign.account_id');
    }

    /**
     * Initialize the DocuSign API client
     */
    protected function initializeDocusignClient()
    {
        try {
            $integrationKey = config('services.docusign.client_id');
            $userId         = config('services.docusign.user_id');
            $baseUrl        = config('services.docusign.base_url');
            $privateKeyPath = storage_path('app/docusign/private.key');
            $impersonatedUserId = config('services.docusign.impersonated_user_id');

                \Log::info('$integrationKey:' . $integrationKey);
            \Log::info('$userId:' . $userId);
            \Log::info('$baseUrl:' . $baseUrl);
            \Log::info('$privateKeyPath:' . $privateKeyPath);
            \Log::info('$impersonatedUserId:' . $impersonatedUserId);
        


            if (!$integrationKey || !$userId || !$baseUrl) {
                Log::error('DocuSign configuration is incomplete.');
                return;
            }

            if (!file_exists($privateKeyPath)) {
                Log::error('DocuSign private key file not found at ' . $privateKeyPath);
                return;
            }

            // $privateKey = trim(file_get_contents($privateKeyPath));
            $privateKey = [
                file_get_contents(storage_path('app/docusign/private.key')),
                ''
            ];
            \Log::info(print_r($privateKey, true));
            if (empty($privateKey)) {
                Log::error('Private key is empty');
                return;
            }

    
            $this->config = new Configuration();
            $this->config->setHost('https://demo.docusign.net/restapi');
          
        

            $this->apiClient = new ApiClient($this->config);
        
           $this->apiClient->getOAuth()->setOAuthBasePath("account-d.docusign.com");

            $response = $this->apiClient->requestJWTUserToken(
                $integrationKey,
                $impersonatedUserId,
                $privateKey,
                ['signature', 'impersonation'],
                3600
            );

            if (!$response || empty($response[0]) || !$response[0]->getAccessToken()) {
                Log::error('Failed to retrieve access token from DocuSign');
                return;
            }

            $accessToken = $response[0]->getAccessToken();
            \Log::info(print_r( $response, true));
            $this->config->addDefaultHeader('Authorization', 'Bearer ' . $accessToken);

       
            $this->apiClient = new ApiClient($this->config);

            Log::info('DocuSign client initialized successfully');
        } catch (\Exception $e) {
            Log::error('Failed to initialize DocuSign client: ' . $e->getMessage());
        }
    }


    /**
     * Create a new envelope with a document for signing
     */
    public function createEnvelope(Request $request)
    {
        try {
            $request->validate([
                'pdf_file_id' => 'required|integer',
                'signer_email' => 'required|email',
                'signer_name' => 'required|string',
            ]);

            if (!$this->apiClient) {
                return $this->httpResponse()
                    ->setError()
                    ->setMessage('DocuSign client not initialized. Please check API key configuration.')
                    ->toApiResponse();
            }

            // Get the PDF file
            $userPdfFile = UserPdfFile::with('pdfFile')->findOrFail($request->pdf_file_id);
            $pdfFile = $userPdfFile->pdfFile;
            
            // Get the translated file if available, otherwise use the source file
            $fileUrl = $userPdfFile->translation && $userPdfFile->translation->full_translated_pdf_path
                ? $userPdfFile->translation->full_translated_pdf_path
                : $pdfFile->source_file;
            
            // Download the file content
            $fileContent = $this->getFileContent($fileUrl);
            if (!$fileContent) {
                return $this->httpResponse()
                    ->setError()
                    ->setMessage('Failed to retrieve PDF file content')
                    ->toApiResponse();
            }

            // Create envelope definition
            $envelopeDefinition = $this->createEnvelopeDefinition(
                $pdfFile->name,
                $fileContent,
                $request->signer_email,
                $request->signer_name
            );

            // Create the envelope
            $envelopesApi = new EnvelopesApi($this->apiClient);
            $envelopeSummary = $envelopesApi->createEnvelope($this->accountId, $envelopeDefinition);

             $userPdfFile->envelope_id = $envelopeSummary->getEnvelopeId();
             $userPdfFile->save();

            return $this->httpResponse()
                ->setData([
                    'envelope_id' => $envelopeSummary->getEnvelopeId(),
                    'status' => $envelopeSummary->getStatus(),
                ])
                ->toApiResponse();
        } catch (ApiException $e) {
            Log::error('DocuSign API Exception: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('DocuSign API Error: ' . $e->getMessage())
                ->toApiResponse();
        } catch (\Exception $e) {
            Log::error('Error creating envelope: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('Error creating envelope: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Create envelope definition with document and recipient
     */
    protected function createEnvelopeDefinition($documentName, $documentContent, $signerEmail, $signerName)
    {
        // Create the document model
        $document = new Document([
            'document_base64' => base64_encode($documentContent),
            'name' => $documentName,
            'file_extension' => 'pdf',
            'document_id' => '1',
        ]);

        // Create the signer recipient with a signature tab
        $signHere = new SignHere([
            'anchor_string' => '/sig1/',
            'anchor_units' => 'pixels',
            'anchor_x_offset' => '20',
            'anchor_y_offset' => '10',
        ]);

        // Add the tabs to the signer
        $signer = new Signer([
            'email' => $signerEmail,
            'name' => $signerName,
            'recipient_id' => '1',
            'routing_order' => '1',
            'tabs' => new Tabs(['sign_here_tabs' => [$signHere]]),
        ]);

        // Add the recipients to the envelope
        $recipients = new Recipients(['signers' => [$signer]]);

        // Create the envelope definition
        return new EnvelopeDefinition([
            'email_subject' => 'Please sign this document',
            'documents' => [$document],
            'recipients' => $recipients,
            'status' => 'sent',
        ]);
    }

    /**
     * Get file content from URL
     */
    protected function getFileContent($fileUrl)
    {
        try {
            // Check if it's a local storage path
            if (Str::startsWith($fileUrl, '/')) {
                return Storage::disk('public')->get($fileUrl);
            }
            
            // Otherwise, fetch from URL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fileUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $fileContent = curl_exec($ch);
            curl_close($ch);
            
            return $fileContent;
        } catch (\Exception $e) {
            Log::error('Error getting file content: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get envelope status
     */
    public function getEnvelopeStatus(Request $request, $envelopeId)
    {
        try {
            if (!$this->apiClient) {
                return $this->httpResponse()
                    ->setError()
                    ->setMessage('DocuSign client not initialized. Please check API key configuration.')
                    ->toApiResponse();
            }
            
            $envelopesApi = new EnvelopesApi($this->apiClient);
            $envelope = $envelopesApi->getEnvelope($this->accountId, $envelopeId);

            return $this->httpResponse()
                ->setData([
                    'envelope_id' => $envelope->getEnvelopeId(),
                    'status' => $envelope->getStatus(),
                    'created_date' => $envelope->getCreatedDateTime(),
                    'sent_date' => $envelope->getSentDateTime(),
                    'completed_date' => $envelope->getCompletedDateTime(),
                ])
                ->toApiResponse();
        } catch (ApiException $e) {
            Log::error('DocuSign API Exception: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('DocuSign API Error: ' . $e->getMessage())
                ->toApiResponse();
        } catch (\Exception $e) {
            Log::error('Error getting envelope status: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('Error getting envelope status: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Create a signing URL for embedded signing
     */
    public function createSigningUrl(Request $request)
    {
        try {
            $request->validate([
                'envelope_id' => 'required|string',
                'return_url' => 'required|url',
            ]);

            if (!$this->apiClient) {
                return $this->httpResponse()
                    ->setError()
                    ->setMessage('DocuSign client not initialized. Please check API key configuration.')
                    ->toApiResponse();
            }

            $envelopesApi = new EnvelopesApi($this->apiClient);
            $recipientViewRequest = new \DocuSign\eSign\Model\RecipientViewRequest([
                'return_url' => $request->return_url,
                'authentication_method' => 'none',
                'recipient_id' => '1',
                'user_name' => 'Trung Do',
                'email' => '<EMAIL>',
            ]);

            $signingView = $envelopesApi->createRecipientView(
                $this->accountId,
                $request->envelope_id,
                $recipientViewRequest
            );

            return $this->httpResponse()
                ->setData([
                    'signing_url' => $signingView->getUrl(),
                ])
                ->toApiResponse();
        } catch (ApiException $e) {
            Log::error('DocuSign API Exception: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('DocuSign API Error: ' . $e->getMessage())
                ->toApiResponse();
        } catch (\Exception $e) {
            Log::error('Error creating signing URL: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('Error creating signing URL: ' . $e->getMessage())
                ->toApiResponse();
        }
    }

    /**
     * Handle DocuSign webhook callbacks
     */
    public function handleWebhook(Request $request)
    {
        try {
            Log::info('DocuSign webhook received', $request->all());
            
            // Validate the webhook payload
            if (!$request->has('event') || !$request->has('data')) {
                return response()->json(['status' => 'error', 'message' => 'Invalid webhook payload']);
            }

            $event = $request->input('event');
            $data = $request->input('data');

            // Process different webhook events
            switch ($event) {
                case 'envelope-completed':
                    // Handle completed envelope
                    $this->handleCompletedEnvelope($data);
                    break;
                    
                case 'envelope-declined':
                    // Handle declined envelope
                    $this->handleDeclinedEnvelope($data);
                    break;
                    
                default:
                    Log::info('Unhandled DocuSign webhook event: ' . $event);
                    break;
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Error handling DocuSign webhook: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Handle completed envelope webhook
     */
    protected function handleCompletedEnvelope($data)
    {
        // Implementation for handling completed envelopes
        // For example, update document status in your database
        Log::info('Envelope completed', $data);
    }

    /**
     * Handle declined envelope webhook
     */
    protected function handleDeclinedEnvelope($data)
    {
        // Implementation for handling declined envelopes
        Log::info('Envelope declined', $data);
    }

    /**
     * Download the signed document
     */
    public function downloadSignedDocument(Request $request, $envelopeId)
    {
        try {
            if (!$this->apiClient) {
                return $this->httpResponse()
                    ->setError()
                    ->setMessage('DocuSign client not initialized. Please check API key configuration.')
                    ->toApiResponse();
            }



            $envelopesApi = new EnvelopesApi($this->apiClient);
            $documentOptions = new \DocuSign\eSign\Api\EnvelopesApi\GetDocumentOptions();
            $documentOptions->setCertificate('true');

            // $documentId = '1'; 
            
            // $documents = $envelopesApi->getDocuments($this->accountId, $envelopeId, $documentOptions);
            $documentId = '1'; // Assuming we're getting the first document
            
            try {
                $documentContent = $envelopesApi->getDocument(
                    $this->accountId,
                    $documentId,                
                    $envelopeId,
                    $documentOptions
                );
            } catch (\Exception $e) {
                return $this->httpResponse()
                ->setError()
                ->setMessage('Error downloading signed document: ' . $e->getMessage())
                ->toApiResponse();
            }

            // Save the signed document
            $fileName = 'signed_document_' . $envelopeId . '.pdf';
            $path = 'signed_documents/' . $fileName;
            Storage::disk('public')->put($path, $documentContent);
            
            return $this->httpResponse()
                ->setData([
                    'file_name' => $fileName,
                    'download_url' => Storage::disk('public')->url($path),
                ])
                ->toApiResponse();
        } catch (ApiException $e) {
            Log::error('DocuSign API Exception: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('DocuSign API Error: ' . $e->getMessage())
                ->toApiResponse();
        } catch (\Exception $e) {
            Log::error('Error downloading signed document: ' . $e->getMessage());
            return $this->httpResponse()
                ->setError()
                ->setMessage('Error downloading signed document: ' . $e->getMessage())
                ->toApiResponse();
        }
    }
}