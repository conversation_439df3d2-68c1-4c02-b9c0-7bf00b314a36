<?php

namespace Botble\Inspection\Enums;

enum TransactionStatus: string
{
    case PENDING = 'pending';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case CANCELED = 'canceled';
    case REFUNDED = 'refunded';

    public static function getValues(): array
    {
        return [
            self::PENDING->value,
            self::COMPLETED->value,
            self::FAILED->value,
            self::CANCELED->value,
            self::REFUNDED->value,
        ];
    }
}
