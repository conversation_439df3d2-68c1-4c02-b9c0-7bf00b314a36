import { Print, Share, Download } from "@mui/icons-material"
import { Box, IconButton, Menu, MenuItem, ListItemIcon, ListItemText, Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Snackbar, Alert } from "@mui/material"
import { InspectionFile } from "services/inspection-file.service"
import { shareFile, printFile, copyToClipboard, downloadFile } from "services/file-actions.service"
import React, { useState } from "react"
import { useDispatch } from "react-redux"

interface InspectionHelperButtonProps {
    inspectionFile: InspectionFile | null;
}

const InspectionHelperButton: React.FC<InspectionHelperButtonProps> = ({ inspectionFile }) => {
    const dispatch = useDispatch();
    const [shareAnchorEl, setShareAnchorEl] = useState<null | HTMLElement>(null);
    const [shareDialogOpen, setShareDialogOpen] = useState(false);
    const [shareEmail, setShareEmail] = useState('');
    const [shareMessage, setShareMessage] = useState('');
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info'>('success');
    const [isLoading, setIsLoading] = useState(false);

    const fileUrl = inspectionFile?.translation?.translated_file_path || '';
    const fileName = inspectionFile?.pdf_file?.name || 'inspection-file';

    const handleShareClick = (event: React.MouseEvent<HTMLElement>) => {
        setShareAnchorEl(event.currentTarget);
    };

    const handleShareClose = () => {
        setShareAnchorEl(null);
    };

    const handleShareDialogOpen = () => {
        setShareDialogOpen(true);
        handleShareClose();
    };

    const handleShareDialogClose = () => {
        setShareDialogOpen(false);
        setShareEmail('');
        setShareMessage('');
    };

    const handleShareSubmit = async () => {
        if (!fileUrl) {
            showSnackbar('No file available to share', 'error');
            return;
        }

        setIsLoading(true);
        try {
            const result = await shareFile(dispatch, {
                fileUrl,
                recipientEmail: shareEmail,
                message: shareMessage,
                fileId: inspectionFile?.id,
                fileType: 'inspection'
            });
            
            if (result.success) {
                showSnackbar(result.message, 'success');
                handleShareDialogClose();
            } else {
                showSnackbar(result.message, 'error');
            }
        } catch (error) {
            showSnackbar('Failed to share file', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const handlePrint = async () => {
        if (!fileUrl) {
            showSnackbar('No file available to print', 'error');
            return;
        }

        const success = await printFile(fileUrl);
        if (!success) {
            showSnackbar('Please allow pop-ups to print the document', 'error');
        }
    };

    const handleDownload = () => {
        if (!fileUrl) {
            showSnackbar('No file available to download', 'error');
            return;
        }

        downloadFile(fileUrl, fileName);
    };

    const handleCopyLink = async () => {
        if (!fileUrl) {
            showSnackbar('No file available to share', 'error');
            handleShareClose();
            return;
        }
        
        const success = await copyToClipboard(fileUrl);
        if (success) {
            showSnackbar('Link copied to clipboard', 'success');
        } else {
            showSnackbar('Failed to copy link', 'error');
        }
        handleShareClose();
    };

    const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' = 'success') => {
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setSnackbarOpen(true);
    };

    const handleSnackbarClose = () => {
        setSnackbarOpen(false);
    };

    return (
        <>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <IconButton 
                    aria-label="print" 
                    color="primary" 
                    onClick={handlePrint}
                    disabled={!fileUrl}
                >
                    <Print />
                </IconButton>
                <IconButton 
                    aria-label="share" 
                    color="primary" 
                    onClick={handleShareClick}
                    disabled={!fileUrl}
                >
                    <Share />
                </IconButton>
                <IconButton 
                    aria-label="download" 
                    color="primary" 
                    onClick={handleDownload}
                    disabled={!fileUrl}
                >
                    <Download />
                </IconButton>
            </Box>

            {/* Share Menu */}
            <Menu
                anchorEl={shareAnchorEl}
                open={Boolean(shareAnchorEl)}
                onClose={handleShareClose}
            >
                <MenuItem onClick={handleShareDialogOpen}>
                    <ListItemIcon>
                        <Share fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Share via Email</ListItemText>
                </MenuItem>
                <MenuItem onClick={handleCopyLink}>
                    <ListItemIcon>
                        <Share fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Copy Link</ListItemText>
                </MenuItem>
            </Menu>

            {/* Share Dialog */}
            <Dialog open={shareDialogOpen} onClose={handleShareDialogClose} maxWidth="sm" fullWidth>
                <DialogTitle>Share Document</DialogTitle>
                <DialogContent>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                        Share this document via email
                    </Typography>
                    
                    <TextField
                        autoFocus
                        margin="dense"
                        id="recipient-email"
                        label="Recipient Email"
                        type="email"
                        fullWidth
                        value={shareEmail}
                        onChange={(e) => setShareEmail(e.target.value)}
                        sx={{ mb: 2 }}
                    />
                    
                    <TextField
                        margin="dense"
                        id="share-message"
                        label="Message (Optional)"
                        multiline
                        rows={4}
                        fullWidth
                        value={shareMessage}
                        onChange={(e) => setShareMessage(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleShareDialogClose} disabled={isLoading}>
                        Cancel
                    </Button>
                    <Button 
                        onClick={handleShareSubmit} 
                        color="primary"
                        disabled={!shareEmail || isLoading}
                    >
                        {isLoading ? 'Sharing...' : 'Share'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar for notifications */}
            <Snackbar
                open={snackbarOpen}
                autoHideDuration={6000}
                onClose={handleSnackbarClose}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
                    {snackbarMessage}
                </Alert>
            </Snackbar>
        </>
    );
};

export default InspectionHelperButton;
