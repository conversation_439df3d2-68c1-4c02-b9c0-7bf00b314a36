import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  Box, Divider, Button, DialogActions, Stack, Typography, Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useDispatch } from 'react-redux';
import AppGridContainer from '@crema/core/AppGridContainer';
import { createInspectionFile } from 'services/inspection-file.service';
import ReactDropzoneBasic from 'modules/thirdParty/reactDropzone/Basic';
import { useRouter } from "next/router";
import LanguageFlag from 'components/LanguageFlag';
import UploadStepper, { TranslationStepKey } from './TranslationStepper';
import CloseIcon from '@mui/icons-material/Close';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';


const StyledDivider = styled(Divider)(({ theme }) => ({
  marginTop: 20,
  marginBottom: 20,
  [theme.breakpoints.up('xl')]: {
    marginTop: 32,
    marginBottom: 32,
  },
}));

const languages = [
  { label: 'Vietnamese', code: 'vi', flag: '/assets/images/flags/vietnam.png' },
  { label: 'Chinese', code: 'zh', flag: '/assets/images/flags/china.png' },
  { label: 'Spanish', code: 'es', flag: '/assets/images/flags/spain.png' },
];

interface CreatePdfFormProps {
  onClose: () => void;
  isOpen: boolean;
}

const InspectionCreatePopup: React.FC<CreatePdfFormProps> = ({ onClose, isOpen }) => {
  const dispatch = useDispatch();
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const [languageSelected, setLanguageSelected] = useState<string>('vi');
  const router = useRouter();

  const handleToggleLanguage = useCallback((code: string) => {
    setLanguageSelected(code);
  }, []);

  const updateUploadedFiles = useCallback((files: File[]) => {
    if (files.length > 0) {
      setPdfFile(files[0]);
    } else {
      setPdfFile(null);
    }
  }, []);

  const handleFormSubmit = async () => {
    if (loading) return;
    setLoading(true);
    const res = await createInspectionFile(dispatch, pdfFile, languageSelected);
    setLoading(false);
    console.log('res', res);
    if (!res || res?.error) {
      alert('Error: ' + res?.error);
      return;
    }

    router.push(`/inspection-files/${res.data.id}`)
  };

  return (
    <Dialog maxWidth="md" fullWidth={true} onClose={onClose} open={isOpen}>
      <DialogTitle>Add Inspection file</DialogTitle>
      <IconButton
        aria-label="close"
        onClick={onClose}
        sx={(theme) => ({
          position: 'absolute',
          right: 8,
          top: 8,
          color: theme.palette.grey[500],
        })}
      >
        <CloseIcon />
      </IconButton>
      <DialogContent>
        <UploadStepper currentStep={TranslationStepKey.Upload} />
        <StyledDivider />

        <Box mb={5}>
          <AppGridContainer spacing={5}>
            <>
              <Grid item xs={12}>
                <Box mb={3}>
                  <ReactDropzoneBasic updateUploadedFiles={updateUploadedFiles} />
                </Box>
                <Typography sx={{ fontSize: '1rem' }} gutterBottom>Select language</Typography>
                <Stack direction='row' spacing={1}>
                  {languages.map((lang) => (
                    <LanguageFlag
                      key={lang.code}
                      language={lang.code}
                      label={lang.label}
                      onClick={() => handleToggleLanguage(lang.code)}
                      selected={languageSelected === lang.code}
                    />
                  ))}
                </Stack>
              </Grid>
              {/* <Grid item xs={12} lg={pdfFile ? 6 : 12}>
                {pdfFile && (
                  <iframe
                    src={URL.createObjectURL(pdfFile)}
                    style={{ width: '100%', height: 400, border: 'none' }}
                  />
                )}
              </Grid> */}
            </>
          </AppGridContainer>
        </Box>

      </DialogContent>

      <DialogActions>
        <Button
          sx={{ gap: 1 }}
          onClick={handleFormSubmit}
          disabled={loading}
          variant='contained'
          color='primary'
          endIcon={loading ? <CircularProgress size={16} color='inherit' /> : <NavigateNextIcon />}
        >
          Next
          {/* {loading && <CircularProgress size={16} color='inherit' />} */}
        </Button>
      </DialogActions>
    </Dialog>
  );
};




export default InspectionCreatePopup;
