import { Box, Button, CircularProgress } from "@mui/material";
import InspectionHelperButton from "./InspectionHelperButton";
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { InspectionFile } from "services/inspection-file.service";

type Props = {
  inspectionFile: InspectionFile;
  loading: boolean;
  fullTranslationLoading: boolean;
  pdfPreviewPath: string;
  onFullTranslate: () => void;
  onPreviewGenerating: () => void;
};

const TranslationActions = ({
  inspectionFile,
  loading,
  fullTranslationLoading,
  pdfPreviewPath,
  onFullTranslate,
  onPreviewGenerating
}: Props) => {
  return (
    <Box sx={{ display: 'flex', gap: 2, flexDirection: 'column' }}>
      <InspectionHelperButton inspectionFile={inspectionFile} />
      {/* {!pdfPreviewPath && (
        <Button
          sx={{ width: 'fit-content' }}
          size='large'
          variant='outlined'
          color='primary'
          onClick={onPreviewGenerating}
          endIcon={loading ? <CircularProgress size={16} color='inherit' /> : <NavigateNextIcon />}
        >
          View trial translation
        </Button>
      )} */}
      {inspectionFile.status !== 'completed' && (
        <Button
          sx={{ width: 'fit-content' }}
          variant='contained'
          color='secondary'
          size='large'
          onClick={onFullTranslate}
          endIcon={fullTranslationLoading ? <CircularProgress size={16} color='inherit' /> : <NavigateNextIcon />}
        >
          Full translation
        </Button>
      )}
    </Box>
  );
};

export default TranslationActions;