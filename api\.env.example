APP_NAME="GoldenScope"
# Disable debug mode by changing APP_DEBUG=false when your site is ready for live.
APP_DEBUG=true
APP_ENV=local
# Must change APP_URL to your website URL. Example: APP_URL=http://your-domain.com
APP_URL=http://localhost:9000
APP_KEY=base64:hMS5VtciEk3t/0Ije8BCRl+AZOvU2gJanbAw5i/LgIs=
LOG_CHANNEL=daily

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Change to your database credentials
DB_CONNECTION=mysql
# If you use Laravel Sail, just change DB_HOST to DB_HOST=mysql
# On some hosting DB_HOST can be localhost instead of 127.0.0.1
DB_HOST=
DB_PORT=3306
DB_DATABASE="inspection_dashboard"
DB_USERNAME="root"
DB_PASSWORD=""
DB_STRICT=false

# You should change this value to hide your admin URL (DON'T use special characters). Example: ADMIN_DIR=hello-admin then your admin URL will be
# http://your-domain.com/hello-admin
ADMIN_DIR=admin

CMS_ENABLE_INSTALLER=true
PDF_TRANSLATION_SERVICE_URL=http://127.0.0.1:9000
WEB_URL=http://127.0.0.1:3000

STRIPE_KEY=pk_test_...
STRIPE_SECRET=sk_test_...
*******************************

DOCUSIGN_CLIENT_ID=************************************
DOCUSIGN_CLIENT_SECRET=c1a6196a-e5fa-47f5-8fd9-ce02b184c34d
DOCUSIGN_REDIRECT_URI=http://localhost:9000/api/v1/docusign/webhook
DOCUSIGN_BASE_URL=https://demo.docusign.net
DOCUSIGN_ACCOUNT_ID=************************************