import jwtAxios from '@crema/services/auth/jwt-auth';
import { Dispatch } from 'redux';
import { fetchError, fetchStart, fetchSuccess } from 'redux/actions/Common';
import { AppActions } from 'types';

export interface CreateEnvelopeParams {
  pdf_file_id: number;
  signer_email: string;
  signer_name: string;
}

export interface CreateSigningUrlParams {
  envelope_id: string;
  return_url: string;
}

export interface EnvelopeResponse {
  envelope_id: string;
  status: string;
}

export interface SigningUrlResponse {
  signing_url: string;
}

export interface EnvelopeStatusResponse {
  envelope_id: string;
  status: string;
  created_date: string;
  sent_date: string;
  completed_date: string;
}

export interface DownloadDocumentResponse {
  file_name: string;
  download_url: string;
}

/**
 * Create a DocuSign envelope for signing
 */
export const createEnvelope = async (
  dispatch: Dispatch<AppActions>,
  params: CreateEnvelopeParams
): Promise<EnvelopeResponse | null> => {
  dispatch(fetchStart());

  try {
    const response = await jwtAxios.post('docusign/create-envelope', params);
    dispatch(fetchSuccess());

    if (response.status === 200 && !response.data.error) {
      return response.data.data;
    } else {
      const errorMessage = response.data.message || 'Failed to create envelope';
      dispatch(fetchError(errorMessage));
      return null;
    }
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message || 'Failed to create envelope';
    dispatch(fetchError(errorMessage));
    return null;
  }
};

/**
 * Create a signing URL for embedded signing
 */
export const createSigningUrl = async (
  dispatch: Dispatch<AppActions>,
  params: CreateSigningUrlParams
): Promise<SigningUrlResponse | null> => {
  dispatch(fetchStart());

  try {
    const response = await jwtAxios.post('docusign/create-signing-url', params);
    dispatch(fetchSuccess());

    if (response.status === 200 && !response.data.error) {
      return response.data.data;
    } else {
      const errorMessage = response.data.message || 'Failed to create signing URL';
      dispatch(fetchError(errorMessage));
      return null;
    }
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message || 'Failed to create signing URL';
    dispatch(fetchError(errorMessage));
    return null;
  }
};

/**
 * Get envelope status
 */
export const getEnvelopeStatus = async (
  dispatch: Dispatch<AppActions>,
  envelopeId: string
): Promise<EnvelopeStatusResponse | null> => {
  dispatch(fetchStart());

  try {
    const response = await jwtAxios.get(`docusign/envelope/${envelopeId}/status`);
    dispatch(fetchSuccess());

    if (response.status === 200 && !response.data.error) {
      return response.data.data;
    } else {
      const errorMessage = response.data.message || 'Failed to get envelope status';
      dispatch(fetchError(errorMessage));
      return null;
    }
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message || 'Failed to get envelope status';
    dispatch(fetchError(errorMessage));
    return null;
  }
};

/**
 * Download signed document
 */
export const downloadSignedDocument = async (
  dispatch: Dispatch<AppActions>,
  envelopeId: string
): Promise<DownloadDocumentResponse | null> => {
  dispatch(fetchStart());

  try {
    const response = await jwtAxios.get(`docusign/envelope/${envelopeId}/download`);
    dispatch(fetchSuccess());

    if (response.status === 200 && !response.data.error) {
      return response.data.data;
    } else {
      const errorMessage = response.data.message || 'Failed to download signed document';
      dispatch(fetchError(errorMessage));
      return null;
    }
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message || 'Failed to download signed document';
    dispatch(fetchError(errorMessage));
    return null;
  }
};