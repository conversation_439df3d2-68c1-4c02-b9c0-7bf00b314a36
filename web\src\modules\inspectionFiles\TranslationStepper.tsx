import { Box, Step, StepLabel, Stepper } from '@mui/material';
import React from 'react';

interface TranslationStepsProps {
  currentStep: StepKey;
  setCurrentStep?: (step: StepKey) => void;
}

export type StepKey = typeof TranslationStepKey[keyof typeof TranslationStepKey];

export const TranslationSteps = [
  { label: 'Upload PDF file & Select language', key: 'upload' },
  { label: 'Preview', key: 'preview' },
  // { label: 'Payment', key: 'payment' },
  { label: 'Translation', key: 'translation' },
  { label: 'Complete', key: 'complete' },
];

export const TranslationStepKey = {
  Upload: 'upload',
  Preview: 'preview',
  // Payment: 'payment',
  Translation: 'translation',
  Complete: 'complete',
} as const;

export const getStepIndexByKey = (key: string) =>
  TranslationSteps.findIndex((step) => step.key === key);


const TranslationStepper: React.FC<TranslationStepsProps> = ({ currentStep, setCurrentStep }) => {

  return (
    <Box mb={5}>
      <Stepper activeStep={getStepIndexByKey(currentStep)} alternativeLabel>
        {TranslationSteps.map((s) => (
          <Step key={s.key}><StepLabel>{s.label}</StepLabel></Step>
        ))}
      </Stepper>
    </Box>
  );
};

export default TranslationStepper;
