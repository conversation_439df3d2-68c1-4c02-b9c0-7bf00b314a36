import React from 'react';
import {AppProps} from 'next/app';
import Head from 'next/head';
import CssBaseline from '@mui/material/CssBaseline';
import {CacheProvider} from '@emotion/react';
import createEmotionCache from '../createEmotionCache';
import AppContextProvider from '../@crema/utility/AppContextProvider';
import {Provider} from 'react-redux';
import AppThemeProvider from '../@crema/utility/AppThemeProvider';
import AppStyleProvider from '../@crema/utility/AppStyleProvider';
import AuthRoutes from '../@crema/utility/AuthRoutes';
import {useStore} from '../redux/store'; // Client-side cache, shared for the whole session of the user in the browser.
import {EmotionCache} from '@emotion/cache';
import '../@crema/services/index';
import '../../public/assets/styles/index.css';
import '../shared/vendors/index.css';
import JW<PERSON>uthAuthProvider from '../@crema/services/auth/jwt-auth/JWTAuthProvider';
import AppInfoView from '../@crema/core/AppInfoView';
import AppErrorDialog from '../@crema/core/AppErrorDialog';
// import { SnackbarProvider } from 'notistack';


// Client-side cache, shared for the whole session of the user in the browser.
const clientSideEmotionCache = createEmotionCache();

interface MyAppProps extends AppProps {
  emotionCache?: EmotionCache;
  pageProps: {
    initialReduxState?: any; // Define the type of initialReduxState appropriately
    [key: string]: any; // Allow other properties if needed
  };
}

export default function MyApp(props: MyAppProps) {
  const {Component, emotionCache = clientSideEmotionCache, pageProps} = props;
  const store = useStore(pageProps.initialReduxState);

  return (
    <CacheProvider value={emotionCache}>
      <Head>
        <title>Mylingodocs</title>
        <meta name='viewport' content='initial-scale=1, width=device-width' />
      </Head>
      <AppContextProvider>
        <Provider store={store}>
          <AppThemeProvider>
            <AppStyleProvider>
              {/* <AppLocaleProvider> */}
                {/* <FirebaseAuthProvider> */}
                  <JWTAuthAuthProvider>
                    <AuthRoutes>
                      <CssBaseline />
                      {/* <SnackbarProvider maxSnack={3}> */}
                      <Component {...pageProps} />
                      <AppInfoView />
                      <AppErrorDialog />
                      {/* </SnackbarProvider> */}
                    </AuthRoutes>
                  </JWTAuthAuthProvider>
                {/* </FirebaseAuthProvider> */}
              {/* </AppLocaleProvider> */}
            </AppStyleProvider>
          </AppThemeProvider>
        </Provider>
      </AppContextProvider>
    </CacheProvider>
  );
}
