import React from 'react';
import AppPage from '@crema/hoc/AppPage';
import asyncComponent from '@crema/utility/asyncComponent';
import { Box } from '@mui/material';
import AppContentView from '@crema/core/AppContentView';
import AppComponentHeader from '@crema/core/AppComponentHeader';

const Activities = asyncComponent(() => import('../../modules/activities/Activities'));

const ActivitiesPage = () => {
  return (
    <>
      <AppComponentHeader
        title="Activities"
        description="View all system activities"
      />
      <Activities />
    </>
  );
};

export default AppPage(() => <ActivitiesPage />); 